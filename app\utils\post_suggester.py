"""
Post Suggester Utility

This module provides functionality for generating LinkedIn post suggestions
including dynamic prompt generation, title generation, and post creation
with parallel processing capabilities.
"""

import json
import random
import re
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any
from app.utils.model_initializer import model
from app.utils.post_creator import generate_post_from_persona_keywords


def generate_dynamic_prompt_types(persona_description: str) -> List[Dict[str, str]]:
    """
    Generate 4 unique, punchy prompt types for LinkedIn content using AI.
    
    Args:
        persona_description (str): Description of the user's persona including keywords
        
    Returns:
        List[Dict[str, str]]: List of 4 prompt types with name, focus, and description
    """
    prompt_types_generation_template = f"""
You are a creative LinkedIn content strategist. Generate 4 unique, punchy prompt types for LinkedIn content.

Persona: {persona_description}

Requirements:
1. Create 4 different, creative content types
2. Keep names SHORT and PUNCHY (1-3 words max)
3. Make focus areas CONCISE and clear
4. Each type should inspire short, impactful content
5. Be specific to the persona's expertise
6. Avoid generic categories - be creative and unique
7. Focus on action-oriented, engaging content

Return ONLY a valid JSON array:
[
  {{
    "name": "Short Punchy Name",
    "focus": "concise focus area",
    "description": "brief description"
  }}
]

Generate exactly 4 unique, punchy prompt types.
"""
    
    temperature = random.uniform(0.9, 1.3)  # Higher temperature for more creativity
    response = model.generate_content(prompt_types_generation_template, use_cache=False, temperature=temperature)
    response_text = response.text.strip()
    
    # Clean up the response text to extract JSON
    if "```json" in response_text:
        start = response_text.find("```json") + 7
        end = response_text.find("```", start)
        if end != -1:
            response_text = response_text[start:end].strip()
    elif "```" in response_text:
        start = response_text.find("```") + 3
        end = response_text.find("```", start)
        if end != -1:
            response_text = response_text[start:end].strip()
    
    try:
        prompt_types = json.loads(response_text)
        # Ensure we have exactly 4 types
        if len(prompt_types) >= 4:
            return prompt_types[:4]
        else:
            # Fallback to creative default types if AI generation fails
            return [
                {
                    "name": "Behind Scenes",
                    "focus": "revealing insider knowledge and experiences",
                    "description": "Share behind-the-scenes insights and personal stories"
                },
                {
                    "name": "Future Vision",
                    "focus": "predicting industry changes and opportunities",
                    "description": "Paint a picture of what's coming next"
                },
                {
                    "name": "Challenge Accepted",
                    "focus": "addressing pain points with solutions",
                    "description": "Tackle real challenges with practical solutions"
                },
                {
                    "name": "Success Story",
                    "focus": "sharing wins and breakthrough moments",
                    "description": "Inspire others with your journey and lessons"
                }
            ]
    except Exception as e:
        print(f"Error parsing prompt types: {e}")
        # Fallback to creative default types
        return [
            {
                "name": "Behind Scenes",
                "focus": "revealing insider knowledge and experiences",
                "description": "Share behind-the-scenes insights and personal stories"
            },
            {
                "name": "Future Vision",
                "focus": "predicting industry changes and opportunities",
                "description": "Paint a picture of what's coming next"
            },
            {
                "name": "Challenge Accepted",
                "focus": "addressing pain points with solutions",
                "description": "Tackle real challenges with practical solutions"
            },
            {
                "name": "Success Story",
                "focus": "sharing wins and breakthrough moments",
                "description": "Inspire others with your journey and lessons"
            }
        ]


def generate_dynamic_prompt(prompt_type: Dict[str, str], persona_description: str) -> str:
    """
    Generate a dynamic prompt using AI for a specific prompt type.
    
    Args:
        prompt_type (Dict[str, str]): The prompt type with name, focus, and description
        persona_description (str): Description of the user's persona
        
    Returns:
        str: Generated prompt text
    """
    prompt_generation_template = f"""
You are a creative LinkedIn content strategist. Generate a concise, eye-catching prompt for LinkedIn content.

Persona: {persona_description}

Creative Type: {prompt_type['name']}
Focus: {prompt_type['focus']}

Requirements:
1. Keep it SHORT and CONCISE (max 1 sentence, 15-25 words)
2. Make it engaging and action-oriented
3. Be specific to the persona's expertise
4. Use strong action words
5. Make it feel personal and authentic
6. Focus on the creative type theme
7. Avoid lengthy explanations - be direct and punchy

Examples of concise prompts:
- "Share the biggest lesson you learned from your biggest failure"
- "What's the one thing you wish you knew when you started?"
- "Reveal your most valuable career insight in one sentence"

Generate ONLY the short prompt, no explanations.
"""
    
    # Add some randomness to ensure variety and creativity
    temperature = random.uniform(1.0, 1.4)  # Higher temperature for more creativity
    response = model.generate_content(prompt_generation_template, use_cache=False, temperature=temperature)
    return response.text.strip()


def generate_title_from_prompt(prompt_text: str) -> str:
    """
    Generate a 3-4 word title from the prompt text using AI.
    
    Args:
        prompt_text (str): The prompt text to generate a title from
        
    Returns:
        str: Generated title (3-4 words)
    """
    try:
        # Clean the prompt text first
        clean_prompt = re.sub(r'<[^>]+>', '', prompt_text).strip()
        clean_prompt = re.sub(r'\*+', '', clean_prompt)
        clean_prompt = clean_prompt.replace('**', '').replace('*', '').strip()

        title_prompt = f"""
        Generate a concise, descriptive title for this LinkedIn post prompt. The title should be exactly 3-4 words that capture the main topic.

        Prompt: "{clean_prompt}"

        Requirements:
        1. Exactly 3-4 words maximum
        2. Descriptive of the main topic
        3. Professional and clear
        4. No special characters or punctuation
        5. Title case formatting

        Examples:
        - "Share your biggest career lesson" → "Career Lesson Sharing"
        - "What's your top productivity tip?" → "Productivity Tips Discussion"
        - "Reveal your most valuable insight" → "Professional Insights Sharing"

        Return ONLY the title, no explanations.
        """

        response = model.generate_content(title_prompt, use_cache=False)
        title = response.text.strip()

        # Clean up the title and ensure it's 3-4 words
        title = re.sub(r'[^\w\s]', '', title).strip()
        words = title.split()

        if len(words) > 4:
            title = ' '.join(words[:4])
        elif len(words) < 2:
            # Fallback to extracting key words from prompt
            prompt_words = clean_prompt.split()
            key_words = [word for word in prompt_words if len(word) > 3 and word.lower() not in ['your', 'what', 'how', 'when', 'where', 'why', 'the', 'and', 'but', 'for', 'with']]
            title = ' '.join(key_words[:3]).title() if key_words else "Professional Discussion"

        return title.title()

    except Exception as e:
        print(f"Error generating title: {e}")
        # Fallback title generation
        clean_prompt = re.sub(r'<[^>]+>', '', prompt_text).strip()
        words = clean_prompt.split()
        key_words = [word for word in words if len(word) > 3][:3]
        return ' '.join(key_words).title() if key_words else "Professional Topic"


def generate_post_for_prompt(prompt: str, request_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Generate a post for a given prompt using the persona keywords from the request.

    Args:
        prompt (str): The prompt to generate a post for
        request_data (Dict[str, Any]): Request data containing persona keywords

    Returns:
        Dict[str, str]: Dictionary containing prompt, title, and content
    """
    # Log the prompt being used for debugging
    print(f"Generating post for prompt: {prompt[:100]}...")

    post = generate_post_from_persona_keywords(
        general_persona_keywords=request_data.get('general_persona_keywords', []),
        tone="Professional",
        style="Informative",
        user_prompt=prompt,
        content_interests=request_data.get('content_persona_keywords'),
        network_interests=request_data.get('network_persona_keywords'),
        add_emojis=True,  # Add emojis to suggest-posts endpoint
        add_hashtags=True,
        use_hook_generator=True,
        length=None  # Use default length for suggest-posts
    )

    # Debug: Log the actual return type and structure
    print(f"Post generation returned type: {type(post)}")
    if isinstance(post, list):
        print(f"List length: {len(post)}")
        if len(post) > 0:
            print(f"First item type: {type(post[0])}")
            if isinstance(post[0], dict):
                print(f"First item keys: {list(post[0].keys())}")
    elif isinstance(post, dict):
        print(f"Dict keys: {list(post.keys())}")
    print(f"Post structure: {str(post)[:200]}...")

    # Handle the correct return format from generate_post_from_persona_keywords
    post_content = ""
    try:
        if isinstance(post, list) and len(post) > 0:
            # Function returns an array of post objects directly
            # CRITICAL FIX #4a: Safe list access with validation
            first_item = post[0]
            if isinstance(first_item, dict) and "content" in first_item:
                post_content = first_item["content"]
            else:
                print(f"Warning: First item in list doesn't have content: {first_item}")
        elif isinstance(post, dict) and "posts" in post:
            # Legacy format with 'posts' key
            # CRITICAL FIX #4b: Safe nested list access with validation
            posts_array = post["posts"]
            if isinstance(posts_array, list) and len(posts_array) > 0:
                first_post = posts_array[0]
                if isinstance(first_post, dict) and "content" in first_post:
                    post_content = first_post["content"]
                else:
                    print(f"Warning: First post doesn't have content: {first_post}")
            else:
                print(f"Warning: Posts array is invalid or empty: {posts_array}")
        elif isinstance(post, dict) and "content" in post:
            # Single post object format
            post_content = post["content"]
        elif isinstance(post, str):
            # Plain string format
            post_content = post
        else:
            print(f"Warning: Unexpected post format: {type(post)}")
            print(f"Post data: {str(post)[:200]}...")

        # Ensure we have some content
        if not post_content or not post_content.strip():
            print("Warning: Post content is empty, using fallback")
            post_content = f"Here's a professional insight about {prompt[:50]}... [Content generation failed, please try again]"

    except Exception as e:
        print(f"Error extracting post content: {e}")
        post_content = f"Here's a professional insight about {prompt[:50]}... [Content extraction failed, please try again]"

    # Remove unwanted markdown formatting (like asterisks) from post_content
    post_content = re.sub(r"\*+", "", post_content)
    post_content = re.sub(r"`+", "", post_content)
    post_content = post_content.replace("**", "").replace("*", "")
    post_content = post_content.strip()

    # Remove unwanted markdown formatting (like asterisks) from prompt as well
    clean_prompt = re.sub(r"\*+", "", prompt)
    clean_prompt = re.sub(r"`+", "", clean_prompt)
    clean_prompt = clean_prompt.replace("**", "").replace("*", "")
    clean_prompt = clean_prompt.strip()

    # Remove hashtags from prompt (Issue 1 fix)
    clean_prompt = re.sub(r'#\w+', '', clean_prompt)
    clean_prompt = re.sub(r'\s+', ' ', clean_prompt).strip()  # Clean up extra spaces

    # Log a brief summary of the generated post for validation
    print(f"Generated post length: {len(post_content)} characters")

    # Generate title from the prompt
    title = generate_title_from_prompt(clean_prompt)

    # Wrap title in HTML <p> tags (Issue 2 fix)
    html_title = f"<p>{title}</p>"

    # Format prompt as HTML
    html_prompt = f"<p>{clean_prompt}</p>"
    return {"prompt": html_prompt, "title": html_title, "content": post_content}


def generate_suggest_posts_response(request_data: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Main function to generate suggest-posts response with prompts, titles, and content.

    Args:
        request_data (Dict[str, Any]): Request data containing persona keywords

    Returns:
        List[Dict[str, str]]: List of dictionaries with prompt, title, and content
    """
    # Build persona description
    persona_description = f"Professional with expertise in: {', '.join(request_data.get('general_persona_keywords', []))}"
    if request_data.get('content_persona_keywords'):
        persona_description += f". Content Interests: {', '.join(request_data['content_persona_keywords'])}"
    if request_data.get('network_persona_keywords'):
        persona_description += f". Network Interests: {', '.join(request_data['network_persona_keywords'])}"

    # Generate dynamic prompt types using AI instead of hardcoded ones
    prompt_types = generate_dynamic_prompt_types(persona_description)

    # Log the generated prompt types for debugging
    print(f"Generated creative prompt types:")
    for i, pt in enumerate(prompt_types):
        print(f"  {i+1}. {pt['name']}: {pt['focus']}")

    # Generate dynamic prompts using AI for each type
    # Generate all prompts concurrently using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=4) as executor:
        prompt_futures = [executor.submit(generate_dynamic_prompt, pt, persona_description) for pt in prompt_types]
        generated_prompts = [future.result() for future in prompt_futures]

    # Filter out any exceptions and ensure we have valid prompts
    valid_prompts = []
    for i, prompt in enumerate(generated_prompts):
        if isinstance(prompt, Exception):
            # Fallback to concise creative prompts if AI generation fails
            fallback_prompts = [
                f"Share your biggest career lesson in one sentence",
                f"What's the one thing you wish you knew when starting out?",
                f"Reveal your most valuable insight about {request_data.get('general_persona_keywords', ['your field'])[0] if request_data.get('general_persona_keywords') else 'your field'}",
                f"Share the moment that changed everything for you"
            ]
            valid_prompts.append(fallback_prompts[i % len(fallback_prompts)])
        else:
            valid_prompts.append(prompt)

    # Log the generated prompts for debugging
    print(f"Generated creative prompts:")
    for i, prompt in enumerate(valid_prompts):
        print(f"  {i+1}. {prompt[:80]}...")

    # Generate posts concurrently using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=4) as executor:
        post_futures = [executor.submit(generate_post_for_prompt, prompt, request_data) for prompt in valid_prompts]
        response = []

        for i, future in enumerate(post_futures):
            try:
                result = future.result()
                if result and isinstance(result, dict) and "content" in result and result["content"].strip():
                    response.append(result)
                else:
                    print(f"Warning: Invalid result for prompt {i+1}: {result}")
                    # Add fallback response with HTML-formatted prompt
                    clean_fallback_prompt = re.sub(r"\*+", "", valid_prompts[i])
                    clean_fallback_prompt = re.sub(r"`+", "", clean_fallback_prompt)
                    clean_fallback_prompt = clean_fallback_prompt.replace("**", "").replace("*", "")
                    clean_fallback_prompt = clean_fallback_prompt.strip()
                    # Remove hashtags from fallback prompt
                    clean_fallback_prompt = re.sub(r'#\w+', '', clean_fallback_prompt)
                    clean_fallback_prompt = re.sub(r'\s+', ' ', clean_fallback_prompt).strip()
                    fallback_title = generate_title_from_prompt(clean_fallback_prompt)
                    response.append({
                        "prompt": f"<p>{clean_fallback_prompt}</p>",
                        "title": f"<p>{fallback_title}</p>",
                        "content": f"Here's a professional insight about {valid_prompts[i][:50]}... [Content generation failed, please try again]"
                    })
            except Exception as e:
                print(f"Error generating post for prompt {i+1}: {e}")
                # Add fallback response with HTML-formatted prompt
                clean_fallback_prompt = re.sub(r"\*+", "", valid_prompts[i])
                clean_fallback_prompt = re.sub(r"`+", "", clean_fallback_prompt)
                clean_fallback_prompt = clean_fallback_prompt.replace("**", "").replace("*", "")
                clean_fallback_prompt = clean_fallback_prompt.strip()
                # Remove hashtags from fallback prompt
                clean_fallback_prompt = re.sub(r'#\w+', '', clean_fallback_prompt)
                clean_fallback_prompt = re.sub(r'\s+', ' ', clean_fallback_prompt).strip()
                fallback_title = generate_title_from_prompt(clean_fallback_prompt)
                response.append({
                    "prompt": f"<p>{clean_fallback_prompt}</p>",
                    "title": f"<p>{fallback_title}</p>",
                    "content": f"Here's a professional insight about {valid_prompts[i][:50]}... [Content generation failed, please try again]"
                })

    # Ensure we have at least some responses
    if not response:
        print("Warning: No posts generated, using fallback responses")
        response = [
            {
                "prompt": "<p>Share your biggest career lesson</p>",
                "title": "<p>Career Lesson Sharing</p>",
                "content": "Every challenge in your career is an opportunity to grow. The key is to approach each obstacle with curiosity rather than fear, and to always ask: 'What can I learn from this?' This mindset has helped me turn setbacks into stepping stones throughout my professional journey."
            }
        ]

    return response
