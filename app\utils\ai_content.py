# ai_content.py
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
import json
import re

def generate_content_ideas(industry, target_audience):
    prompt = TEMPLATES["content_idea"].format(industry=industry, audience=target_audience)
    response = model.generate_content(prompt)
    return response.text

def _category_description(name):
    """Return a meaningful description for a given category name."""
    desc_map = {
        "Leadership": "Insights and strategies on leading teams and organizations.",
        "Innovation": "Latest trends and creative ideas driving change in the industry.",
        "Sustainability": "Practices and news about sustainable business and eco-friendly solutions.",
        "Technology": "Updates and analysis on emerging technologies and digital transformation.",
        "Entrepreneurship": "Advice and stories for starting and growing businesses.",
        "Marketing": "Strategies and tips for effective marketing and brand growth.",
        "Productivity": "Tools and techniques to boost efficiency and performance.",
        "Networking": "Building professional relationships and expanding your network.",
        "Career Development": "Guidance for advancing your career and professional skills.",
        "Diversity": "Discussions on diversity, equity, and inclusion in the workplace.",
        "AI": "Applications and impact of artificial intelligence in various fields.",
        "Finance": "Financial planning, investment, and economic trends.",
        "Health": "Wellness, mental health, and work-life balance tips.",
        "Sales": "Best practices and strategies for sales professionals.",
        "Customer Experience": "Improving customer satisfaction and engagement."
    }
    if name in desc_map:
        return desc_map[name]
    else:
        try:
            prompt = f"Write a short, engaging 1-sentence description for a LinkedIn content category about '{name}'. Use simple, clear words that everyone can understand."
            response = model.generate_content(prompt)
            return response.text.strip()
        except Exception:
            return f"Explore the latest trends, insights, and best practices in {name.lower()}."

def generate_content_categories(
    general_persona_keywords=None,
    content_persona_keywords=None,
    network_persona_keywords=None,
    categories=None
):
    """
    Generate unique, eye-catching content categories (1-2 words each) based on persona keywords.
    When categories are provided, generates 3 related categories for each input category.
    When categories are empty/null, generates up to 10 categories based on persona keywords.
    All arguments are optional lists of strings.
    
    Features:
    - Categories are unique (no duplicates)
    - Categories are eye-catching and compelling (1-2 words max)
    - Categories reflect the specific context of the persona
    - Uses action words, trending terms, and industry buzzwords
    
    Returns a list of dicts: [{"name": ..., "description": ...}, ...]
    """
    # If categories are provided and not empty, generate 3 related categories for each
    if categories and len(categories) > 0:
        result = []
        global_seen_names = set()
        max_attempts = 5  # Prevent infinite loops
        for category in categories:
            unique_related = []
            local_seen = set()
            attempts = 0
            while len(unique_related) < 3 and attempts < max_attempts:
                related_categories = _generate_related_categories(
                    category, 
                    general_persona_keywords, 
                    content_persona_keywords, 
                    network_persona_keywords
                )
                for cat in related_categories:
                    name_key = cat["name"].strip().lower()
                    if name_key not in local_seen and name_key not in global_seen_names:
                        local_seen.add(name_key)
                        global_seen_names.add(name_key)
                        unique_related.append(cat)
                    if len(unique_related) == 3:
                        break
                attempts += 1
            result.append({
                "input": category,
                "related": unique_related
            })
        return result

    # Improved logic: synthesize 10 broad categories using AI
    prompt = f"""
    You are a LinkedIn content strategist specializing in creating eye-catching, engaging content categories.
    Based on the following persona keywords, create exactly 10 UNIQUE, EYE-CATCHING content categories (1-2 words each) that would be highly relevant for creating LinkedIn posts for this specific persona.

    VOCABULARY REQUIREMENTS:
    - Use simple, clear words that everyone can understand
    - Avoid complex or fancy vocabulary - choose common, everyday words instead
    - Write at a level that is easy for all professionals to read and understand
    - Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')
    - Focus on clarity and understanding over sounding sophisticated
    - Make content accessible to professionals of all backgrounds and education levels

    CRITICAL REQUIREMENTS:
    - Each category must be UNIQUE (no duplicates or similar variations)
    - Each category must be EYE-CATCHING and compelling (use action words, trending terms, or industry buzzwords)
    - Each category must reflect the specific context and interests of this persona
    - Maximum 1-2 words per category name
    - Do NOT simply use the keywords as category names
    - Do NOT use company names, regional names, or specific identifiers in category names
    - Create categories that group related interests, topics, and professional themes in an engaging way
    - Focus on professional expertise, industry trends, and universal concepts
    
    Persona keywords:
    - General: {', '.join(general_persona_keywords or [])}
    - Content: {', '.join(content_persona_keywords or [])}
    - Network: {', '.join(network_persona_keywords or [])}
    
    Examples of eye-catching categories:
    - "Tech Disruption" (instead of just "Technology")
    - "Growth Hacking" (instead of just "Marketing")
    - "Future of Work" (instead of just "Leadership")
    - "AI Revolution" (instead of just "Artificial Intelligence")
    - "Quality Mastery" (instead of just "Quality Assurance")
    - "Test Automation" (instead of just "Testing")
    
    AVOID: Company names, regional names, or overly specific identifiers
    
    Return the response in this exact JSON format:
    {{
      "categories": [
        {{"name": "Category Name", "description": "Brief, engaging description that reflects persona context"}},
        ...
      ]
    }}
    Return ONLY the JSON response, no additional text.
    """
    response = model.generate_content(prompt)
    response_text = response.text.strip()
    # Clean up the response text to extract JSON
    if "```json" in response_text:
        start = response_text.find("```json") + 7
        end = response_text.find("```", start)
        if end != -1:
            response_text = response_text[start:end].strip()
    elif "```" in response_text:
        start = response_text.find("```") + 3
        end = response_text.find("```", start)
        if end != -1:
            response_text = response_text[start:end].strip()
    # Parse the JSON response
    try:
        result = json.loads(response_text)
        if "categories" in result:
            # Remove duplicate category names (case-insensitive)
            seen_names = set()
            unique_categories = []
            for cat in result["categories"]:
                name_key = cat["name"].strip().lower()
                if name_key not in seen_names:
                    seen_names.add(name_key)
                    unique_categories.append(cat)
            return unique_categories
        else:
            raise ValueError("No 'categories' key found in response")
    except Exception as e:
        print(f"Error parsing AI categories: {e}")
        # Fallback: use the old keyword-based logic
        categories = []
        seen = set()
        all_keywords = []
        if general_persona_keywords:
            all_keywords.extend(general_persona_keywords)
        if content_persona_keywords:
            all_keywords.extend(content_persona_keywords)
        if network_persona_keywords:
            all_keywords.extend(network_persona_keywords)
        for kw in all_keywords:
            cleaned = re.sub(r'[^a-zA-Z0-9 ]', '', kw).strip()
            if cleaned:
                # Create more eye-catching category names
                words = cleaned.title().split()
                if len(words) >= 2:
                    # Combine words to create eye-catching phrases
                    name = f"{words[0]} {words[1]}"
                else:
                    # Add action words to make it more engaging
                    action_words = ["Revolution", "Mastery", "Innovation", "Excellence", "Strategy", "Success"]
                    name = f"{words[0]} {action_words[len(categories) % len(action_words)]}"
                
                if name not in seen:
                    seen.add(name)
                    description = _category_description(name)
                    categories.append({
                        "name": name,
                        "description": description
                    })
            if len(categories) >= 10:
                break
        return categories

def _generate_related_categories(category, general_persona_keywords=None, content_persona_keywords=None, network_persona_keywords=None):
    """
    Generate 3 unique, eye-catching related categories for a given category using AI.
    Categories are designed to be compelling and reflect the persona context.
    """
    try:
        # Improved prompt for truly related categories
        prompt = f"""
        You are a LinkedIn content strategist specializing in creating eye-catching, engaging content categories.
        For the category: "{category}", generate 3 UNIQUE, EYE-CATCHING, and closely related content categories that someone interested in {category} would also care about.

        VOCABULARY REQUIREMENTS:
        - Use simple, clear words that everyone can understand
        - Avoid complex or fancy vocabulary - choose common, everyday words instead
        - Write at a level that is easy for all professionals to read and understand
        - Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')
        - Focus on clarity and understanding over sounding sophisticated
        - Make content accessible to professionals of all backgrounds and education levels

        CRITICAL REQUIREMENTS:
        - Each category must be UNIQUE and distinct from the original "{category}"
        - Each category must be EYE-CATCHING and compelling (use action words, trending terms, or industry buzzwords)
        - Each category must reflect the specific context and interests of this persona
        - Maximum 1-2 words per category name
        - Do NOT use the original category name or just add suffixes like 'insights', 'trends', etc.
        - Do NOT use company names, regional names, or specific identifiers in category names
        - Each should be a real, recognized topic or professional area related to "{category}"
        - Focus on professional expertise, industry trends, and universal concepts
        
        Examples of eye-catching related categories:
        - If input is "android development": "Mobile Revolution", "App Monetization", "Kotlin Mastery"
        - If input is "digital marketing": "Growth Hacking", "Content Strategy", "Social Domination"
        - If input is "leadership": "Future of Work", "Team Excellence", "Strategic Vision"
        - If input is "AI testing": "Quality Mastery", "Test Automation", "AI Innovation"
        
        AVOID: Company names, regional names, or overly specific identifiers
        
        Persona context:
        - General persona keywords: {', '.join(general_persona_keywords) if general_persona_keywords else 'None'}
        - Content persona keywords: {', '.join(content_persona_keywords) if content_persona_keywords else 'None'}
        - Network persona keywords: {', '.join(network_persona_keywords) if network_persona_keywords else 'None'}
        
        Return the response in this exact JSON format:
        {{
            "categories": [
                {{"name": "Category Name", "description": "Brief, engaging description that reflects persona context"}},
                {{"name": "Category Name", "description": "Brief, engaging description that reflects persona context"}},
                {{"name": "Category Name", "description": "Brief, engaging description that reflects persona context"}}
            ]
        }}
        
        Return ONLY the JSON response, no additional text.
        """
        
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        # Parse the JSON response
        result = json.loads(response_text)
        if "categories" in result:
            return result["categories"]
        else:
            raise ValueError("No 'categories' key found in response")
            
    except Exception as e:
        print(f"Error generating related categories for '{category}': {e}")
        # Improved fallback: use a mapping for common categories with eye-catching names
        fallback_map = {
            "android development": [
                {"name": "Mobile Revolution", "description": "Building great apps for the mobile-first world."},
                {"name": "Kotlin Mastery", "description": "Programming skills with modern Android development."},
                {"name": "App Monetization", "description": "Turning mobile apps into profitable business ventures."}
            ],
            "digital marketing": [
                {"name": "Growth Hacking", "description": "Fast growth strategies for digital business growth."},
                {"name": "Content Strategy", "description": "Smart content planning that drives engagement and conversions."},
                {"name": "Social Domination", "description": "Mastering social media for maximum brand impact."}
            ],
            "leadership": [
                {"name": "Future of Work", "description": "Leading teams in the changing workplace."},
                {"name": "Strategic Vision", "description": "Creating and executing bold business strategies."},
                {"name": "Team Excellence", "description": "Building high-performing teams that deliver results."}
            ],
            "artificial intelligence": [
                {"name": "AI Revolution", "description": "Changing industries with artificial intelligence."},
                {"name": "Machine Learning", "description": "Building smart systems that learn and adapt."},
                {"name": "Data Science", "description": "Getting insights from data to drive decisions."}
            ],
            "entrepreneurship": [
                {"name": "Startup Success", "description": "Building and growing successful business ventures."},
                {"name": "Innovation Hub", "description": "Creating breakthrough products and services."},
                {"name": "Business Scaling", "description": "Growing businesses from startup to enterprise."}
            ],
            # Add more mappings as needed
        }
        key = category.strip().lower()
        if key in fallback_map:
            return fallback_map[key]
        # Generic fallback with eye-catching names
        return [
            {
                "name": f"{category.title()} Innovation",
                "description": f"Latest developments and breakthroughs in {category.lower()}"
            },
            {
                "name": f"{category.title()} Mastery",
                "description": f"Advanced techniques and expertise in {category.lower()}"
            },
            {
                "name": f"{category.title()} Revolution",
                "description": f"Transformative changes and trends in {category.lower()}"
            }
        ]