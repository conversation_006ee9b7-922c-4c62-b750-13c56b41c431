"""
Specialized Agents for LinkedIn Content Creation Chatbot
Each agent handles specific types of user requests and has access to relevant tools.
"""

from typing import List, Dict, Any, Optional
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_google_vertexai import ChatVertexA<PERSON>
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain_core.tools import Tool
import json
import asyncio

from app.chatbot.tools import linkedin_tools, TOOL_MAP
from app.chatbot.state import ConversationState
from config import PROJECT_ID, LOCATION, MODEL_NAME

# Initialize the LLM
def get_llm():
    """Get the Vertex AI LLM instance"""
    return ChatVertexAI(
        model_name=MODEL_NAME,
        project=PROJECT_ID,
        location=LOCATION,
        temperature=0.7,
        max_output_tokens=2048
    )

class IntentRouterAgent:
    """Routes user requests to appropriate specialist agents"""
    
    def __init__(self):
        self.llm = get_llm()
        self.intent_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are an intelligent intent classifier for a LinkedIn content creation assistant.
            
            Analyze the user's message and classify it into one of these intents:
            
            1. **content_creation** - User wants to create, write, or generate LinkedIn posts
               Examples: "write a post about AI", "create content for my business", "help me post about my achievement"
            
            2. **content_modification** - User wants to modify, edit, or improve existing content
               Examples: "make this post more professional", "add emojis to this", "change the tone", "make it shorter"
            
            3. **news_discovery** - User wants trending news, industry updates, or content inspiration
               Examples: "what's trending in tech?", "show me industry news", "find articles about my field"
            
            4. **profile_analysis** - User wants profile analysis, persona generation, or optimization advice
               Examples: "analyze my profile", "what's my professional persona?", "score my LinkedIn"
            
            5. **strategy_planning** - User wants content strategy, scheduling, or posting advice
               Examples: "plan my content calendar", "when should I post?", "create a posting strategy"
            
            6. **general_chat** - General conversation, greetings, or unclear requests
               Examples: "hello", "how are you?", "what can you do?", unclear or conversational messages
            
            User Context: {user_context}
            Conversation History: {conversation_context}
            
            Respond with only the intent name (e.g., "content_creation") and confidence level (0-1).
            Format: intent_name:confidence_score
            """),
            ("human", "{user_message}")
        ])
    
    async def classify_intent(self, user_message: str, state: ConversationState) -> tuple[str, float]:
        """Classify user intent and return intent name and confidence"""
        try:
            user_context = state.get_user_context_summary()
            conversation_context = state.get_conversation_context(last_n_messages=5)
            
            response = await self.llm.ainvoke(
                self.intent_prompt.format_messages(
                    user_message=user_message,
                    user_context=user_context,
                    conversation_context=conversation_context
                )
            )
            
            # Parse response
            result = response.content.strip()
            if ":" in result:
                intent, confidence_str = result.split(":", 1)
                confidence = float(confidence_str)
                return intent.strip(), confidence
            else:
                return "general_chat", 0.5
                
        except Exception as e:
            print(f"Intent classification error: {e}")
            return "general_chat", 0.5

class ContentCreationAgent:
    """Handles LinkedIn post creation requests"""
    
    def __init__(self):
        self.llm = get_llm()
        self.tools = [TOOL_MAP["create_linkedin_post"], TOOL_MAP["generate_categories"]]
        
        self.system_prompt = """You are a LinkedIn content creation specialist. You help users create engaging, professional LinkedIn posts.

Your capabilities:
- Create original LinkedIn posts based on user requirements
- Generate content categories for planning
- Adapt content to different tones, styles, and lengths
- Provide suggestions for improving content engagement

VOCABULARY REQUIREMENTS:
- Use simple, clear words that everyone can understand
- Avoid complex or fancy vocabulary - choose common, everyday words instead
- Write at a level that is easy for all professionals to read and understand
- Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')
- Focus on clarity and understanding over sounding sophisticated
- Make content accessible to professionals of all backgrounds and education levels

User Context: {user_context}

Guidelines:
1. Always ask clarifying questions if the request is unclear
2. Use the user's persona keywords when available
3. Suggest improvements or alternatives
4. Be creative but maintain professionalism
5. Offer to generate multiple variations when appropriate

Available Tools:
- create_linkedin_post: Generate LinkedIn posts
- generate_categories: Create content categories
"""
    
    async def handle_request(self, user_message: str, state: ConversationState) -> str:
        """Handle content creation requests"""
        try:
            # Extract user context
            user_context = state.get_user_context_summary()
            profile = state.user_profile
            
            # Create the prompt
            messages = [
                SystemMessage(content=self.system_prompt.format(user_context=user_context)),
                HumanMessage(content=f"User request: {user_message}")
            ]
            
            # If user has persona keywords, try to create content directly
            if profile.general_persona_keywords:
                try:
                    # Use the tool to create content
                    tool_input = {
                        "persona_keywords": profile.general_persona_keywords,
                        "content_keywords": profile.content_persona_keywords,
                        "network_keywords": profile.network_persona_keywords,
                        "tone": profile.preferred_tone,
                        "style": profile.preferred_style,
                        "user_prompt": user_message
                    }
                    
                    result = TOOL_MAP["create_linkedin_post"].func(json.dumps(tool_input))
                    
                    # Store the generated content
                    state.add_generated_content("linkedin_post", {"content": result, "prompt": user_message})
                    
                    return f"Here's your LinkedIn post:\n\n{result}\n\nWould you like me to modify anything or create alternative versions?"
                    
                except Exception as e:
                    return f"I encountered an issue creating your post: {str(e)}. Could you provide more details about what you'd like to post about?"
            else:
                # Need more information about the user
                return """I'd love to help you create a LinkedIn post! To generate the most relevant content, I need to know more about you.

Could you tell me:
1. What's your professional role or industry?
2. What topics are you interested in posting about?
3. What tone would you prefer (Professional, Casual, Enthusiastic, etc.)?

Or, if you'd like, you can just tell me the specific topic or message you want to share, and I'll work with that!"""
                
        except Exception as e:
            return f"I'm sorry, I encountered an error while processing your request: {str(e)}. Could you try rephrasing your request?"

class ContentModificationAgent:
    """Handles content modification and editing requests"""
    
    def __init__(self):
        self.llm = get_llm()
        self.tools = [TOOL_MAP["modify_content"]]
        
        self.system_prompt = """You are a LinkedIn content editor specialist. You help users improve and modify their existing content.

Your capabilities:
- Modify tone, style, and length of existing posts
- Add or remove emojis and hashtags
- Rewrite content for different audiences
- Provide editing suggestions

VOCABULARY REQUIREMENTS:
- Use simple, clear words that everyone can understand
- Avoid complex or fancy vocabulary - choose common, everyday words instead
- Write at a level that is easy for all professionals to read and understand
- Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')
- Focus on clarity and understanding over sounding sophisticated
- Make content accessible to professionals of all backgrounds and education levels

User Context: {user_context}

Guidelines:
1. Always ask to see the content that needs modification
2. Clarify what specific changes the user wants
3. Explain the changes you're making
4. Offer multiple options when appropriate
5. Keep the original message while improving presentation
"""
    
    async def handle_request(self, user_message: str, state: ConversationState) -> str:
        """Handle content modification requests"""
        try:
            user_context = state.get_user_context_summary()
            
            # Check if there's recent generated content to modify
            recent_content = None
            if state.generated_content:
                recent_post = next((item for item in reversed(state.generated_content) 
                                 if item["type"] == "linkedin_post"), None)
                if recent_post:
                    recent_content = recent_post["content"]["content"]
            
            # Look for content in the message or conversation
            content_to_modify = self._extract_content_from_message(user_message)
            
            if content_to_modify:
                # Detect what modifications are needed
                modifications = self._detect_modifications(user_message)
                
                try:
                    tool_input = {
                        "original_content": content_to_modify,
                        **modifications
                    }
                    
                    result = TOOL_MAP["modify_content"].func(json.dumps(tool_input))
                    
                    return f"Here's your modified content:\n\n{result}\n\nIs this what you were looking for? I can make further adjustments if needed."
                    
                except Exception as e:
                    return f"I encountered an issue modifying your content: {str(e)}. Could you clarify what changes you'd like me to make?"
            
            elif recent_content:
                return f"I see you have this recent post that I can modify:\n\n{recent_content}\n\nWhat changes would you like me to make? I can adjust the tone, style, length, add emojis or hashtags, or make other modifications."
            
            else:
                return """I'd be happy to help modify your content! Please share:

1. The content you'd like me to modify (paste the text)
2. What changes you want (tone, style, length, add emojis, etc.)

For example:
- "Make this more casual"
- "Add emojis and make it shorter"  
- "Change the tone to be more professional"
- "Rewrite this for a technical audience"
"""
                
        except Exception as e:
            return f"I'm sorry, I encountered an error: {str(e)}. Could you share the content you'd like me to modify?"
    
    def _extract_content_from_message(self, message: str) -> Optional[str]:
        """Extract content to be modified from user message"""
        # Look for quoted content or content after keywords
        keywords = ["modify this:", "change this:", "edit this:", "rewrite this:", "improve this:"]
        
        for keyword in keywords:
            if keyword in message.lower():
                parts = message.lower().split(keyword, 1)
                if len(parts) > 1:
                    return parts[1].strip()
        
        # If message is mostly content (more than 50 characters), assume it's content to modify
        if len(message) > 50 and not any(word in message.lower() for word in ["help", "can you", "please", "how to"]):
            return message
        
        return None
    
    def _detect_modifications(self, message: str) -> Dict[str, Any]:
        """Detect what modifications are requested"""
        message_lower = message.lower()
        modifications = {}
        
        # Tone detection
        if any(word in message_lower for word in ["professional", "formal"]):
            modifications["tone"] = "Professional"
        elif any(word in message_lower for word in ["casual", "friendly", "informal"]):
            modifications["tone"] = "Casual"
        elif any(word in message_lower for word in ["enthusiastic", "excited"]):
            modifications["tone"] = "Enthusiastic"
        
        # Style detection
        if any(word in message_lower for word in ["informative", "educational"]):
            modifications["style"] = "Informative"
        elif any(word in message_lower for word in ["story", "personal", "narrative"]):
            modifications["style"] = "Personal storytelling"
        elif any(word in message_lower for word in ["thought-provoking", "insights"]):
            modifications["style"] = "Thought-provoking"
        
        # Length detection
        if any(word in message_lower for word in ["shorter", "brief", "concise"]):
            modifications["length"] = "short"
        elif any(word in message_lower for word in ["longer", "detailed", "expand"]):
            modifications["length"] = "long"
        
        # Emoji and hashtag detection
        if any(word in message_lower for word in ["emoji", "emojis"]):
            modifications["add_emojis"] = True
        if any(word in message_lower for word in ["hashtag", "hashtags", "#"]):
            modifications["add_hashtags"] = True
        
        return modifications

class NewsDiscoveryAgent:
    """Handles news discovery and content inspiration requests"""
    
    def __init__(self):
        self.llm = get_llm()
        self.tools = [TOOL_MAP["get_trending_news"]]
        
        self.system_prompt = """You are a news discovery specialist for LinkedIn content creators. You help users find relevant industry news and trends.

Your capabilities:
- Find trending news in specific industries
- Discover content inspiration
- Provide news summaries and insights
- Suggest content angles based on current events

VOCABULARY REQUIREMENTS:
- Use simple, clear words that everyone can understand
- Avoid complex or fancy vocabulary - choose common, everyday words instead
- Write at a level that is easy for all professionals to read and understand
- Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')
- Focus on clarity and understanding over sounding sophisticated
- Make content accessible to professionals of all backgrounds and education levels

User Context: {user_context}

Guidelines:
1. Focus on work-related and industry-relevant news
2. Provide summaries and key insights
3. Suggest how news can be turned into LinkedIn content
4. Ask for specific industries or topics if unclear
5. Offer to find news for different time periods
"""
    
    async def handle_request(self, user_message: str, state: ConversationState) -> str:
        """Handle news discovery requests"""
        try:
            user_context = state.get_user_context_summary()
            profile = state.user_profile
            
            # Extract any specific categories or topics from the message
            categories = self._extract_categories_from_message(user_message)
            
            if profile.general_persona_keywords or categories:
                try:
                    tool_input = {
                        "persona_keywords": profile.general_persona_keywords or ["business"],
                        "content_keywords": profile.content_persona_keywords,
                        "network_keywords": profile.network_persona_keywords,
                        "categories": categories
                    }
                    
                    result = await asyncio.create_task(
                        asyncio.to_thread(TOOL_MAP["get_trending_news"].func, json.dumps(tool_input))
                    )
                    
                    if result and "No trending news found" not in result:
                        suggestions = self._generate_content_suggestions(result)
                        return f"{result}\n\n**Content Ideas:**\n{suggestions}"
                    else:
                        return "I couldn't find specific trending news for your interests right now. This might be due to API limitations or lack of recent relevant articles. Would you like me to suggest some general content ideas instead?"
                        
                except Exception as e:
                    return f"I encountered an issue fetching news: {str(e)}. Would you like me to suggest some general content topics for your industry?"
            else:
                return """I'd be happy to find relevant news for you! To get the most relevant results, could you tell me:

1. What industry or field are you interested in?
2. Any specific topics or areas of focus?
3. Are you looking for recent news (last week) or broader trends?

For example:
- "Find AI and technology news"
- "What's trending in marketing this week?"
- "Show me healthcare industry updates"
"""
                
        except Exception as e:
            return f"I'm sorry, I encountered an error while fetching news: {str(e)}. Could you try rephrasing your request?"
    
    def _extract_categories_from_message(self, message: str) -> Optional[List[str]]:
        """Extract specific categories or topics from user message"""
        # Common industry/topic keywords
        topics = {
            "ai": "AI Technology",
            "artificial intelligence": "AI Technology", 
            "technology": "Technology",
            "tech": "Technology",
            "marketing": "Marketing",
            "healthcare": "Healthcare",
            "finance": "Finance",
            "fintech": "FinTech",
            "startup": "Startups",
            "business": "Business",
            "leadership": "Leadership",
            "management": "Management",
            "data science": "Data Science",
            "cybersecurity": "Cybersecurity",
            "cloud": "Cloud Computing",
            "remote work": "Remote Work",
            "sustainability": "Sustainability"
        }
        
        message_lower = message.lower()
        found_categories = []
        
        for keyword, category in topics.items():
            if keyword in message_lower:
                found_categories.append(category)
        
        return found_categories if found_categories else None
    
    def _generate_content_suggestions(self, news_summary: str) -> str:
        """Generate content suggestions based on news"""
        suggestions = [
            "💡 Share your expert opinion on one of these developments",
            "📊 Create a post about what this means for your industry",
            "🔮 Discuss what you think will happen next with these trends",
            "💬 Ask your network what they think about these topics",
            "📚 Share lessons learned or best practices related to these trends"
        ]

        return "\n".join(suggestions)

class ProfileAnalysisAgent:
    """Handles profile analysis and persona generation requests"""
    
    def __init__(self):
        self.llm = get_llm()
        self.tools = [TOOL_MAP["generate_persona"], TOOL_MAP["score_profile"]]
        
        self.system_prompt = """You are a LinkedIn profile optimization specialist. You help users analyze and improve their professional profiles.

Your capabilities:
- Generate professional personas from profile data
- Score and analyze LinkedIn profiles
- Provide optimization recommendations
- Identify strengths and improvement areas

VOCABULARY REQUIREMENTS:
- Use simple, clear words that everyone can understand
- Avoid complex or fancy vocabulary - choose common, everyday words instead
- Write at a level that is easy for all professionals to read and understand
- Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')
- Focus on clarity and understanding over sounding sophisticated
- Make content accessible to professionals of all backgrounds and education levels

User Context: {user_context}

Guidelines:
1. Ask for profile information if not available
2. Provide actionable recommendations
3. Focus on professional growth and networking
4. Explain the reasoning behind suggestions
5. Offer specific examples and improvements
"""
    
    async def handle_request(self, user_message: str, state: ConversationState) -> str:
        """Handle profile analysis requests"""
        try:
            user_context = state.get_user_context_summary()
            
            # Check if user is asking for persona generation or profile scoring
            if any(word in user_message.lower() for word in ["persona", "identity", "keywords"]):
                return await self._handle_persona_generation(user_message, state)
            elif any(word in user_message.lower() for word in ["score", "rate", "analyze", "optimize"]):
                return await self._handle_profile_scoring(user_message, state)
            else:
                return """I can help you with LinkedIn profile analysis! I offer two main services:

**1. Persona Generation** - I'll analyze your profile to create:
   - Professional persona keywords
   - Content interests 
   - Network recommendations

**2. Profile Scoring** - I'll evaluate your profile for:
   - Completeness and optimization
   - Professional presentation
   - Specific improvement suggestions

Which would you like help with? You can also share your LinkedIn profile information and I'll provide both analyses!"""
                
        except Exception as e:
            return f"I'm sorry, I encountered an error: {str(e)}. Could you tell me more about what kind of profile analysis you're looking for?"
    
    async def _handle_persona_generation(self, user_message: str, state: ConversationState) -> str:
        """Handle persona generation requests"""
        # Check if we have profile data in the conversation state
        if hasattr(state.user_profile, 'general_persona_keywords') and state.user_profile.general_persona_keywords:
            return f"""Based on your profile, here's your professional persona:

**General Persona:** {', '.join(state.user_profile.general_persona_keywords)}
**Content Interests:** {', '.join(state.user_profile.content_persona_keywords or ['Not specified'])}
**Network Interests:** {', '.join(state.user_profile.network_persona_keywords or ['Not specified'])}

Would you like me to refine this persona or help you create content based on these insights?"""
        else:
            return """To generate your professional persona, I'll need some information about your profile. Could you share:

**Basic Information:**
- Your current role/position
- Industry you work in
- Key skills and expertise areas

**Experience:**
- Company names and positions (recent 2-3 roles)
- Key achievements or responsibilities

**Optional:**
- Education background
- Certifications
- Languages
- Areas of interest

You can share as much or as little as you're comfortable with, and I'll generate persona keywords to help with content creation!"""
    
    async def _handle_profile_scoring(self, user_message: str, state: ConversationState) -> str:
        """Handle profile scoring requests"""
        return """I'd be happy to score your LinkedIn profile! To provide an accurate analysis, I'll need your profile information:

**Required for Scoring:**
- Name and headline
- Summary/about section
- Work experience (positions, companies, descriptions)
- Education
- Skills
- Current role and industry

**Optional but Helpful:**
- Certifications
- Languages
- Organizations/volunteering
- Number of connections (if you'd like to share)

You can copy and paste sections from your LinkedIn profile, and I'll provide:
- Overall score (0-100)
- Section-by-section analysis
- Specific improvement recommendations
- Optimization tips

Share whatever you're comfortable with, and I'll analyze what you provide!"""

class StrategyPlanningAgent:
    """Handles content strategy and planning requests"""
    
    def __init__(self):
        self.llm = get_llm()
        self.tools = [TOOL_MAP["schedule_posts"], TOOL_MAP["generate_categories"]]
        
        self.system_prompt = """You are a LinkedIn content strategy specialist. You help users plan and optimize their content approach.

Your capabilities:
- Create content calendars and posting schedules
- Suggest content categories and themes
- Provide timing and frequency recommendations
- Develop audience engagement strategies

User Context: {user_context}

Guidelines:
1. Consider user's industry and audience
2. Provide realistic and actionable plans
3. Explain the reasoning behind recommendations
4. Offer flexibility and customization options
5. Focus on sustainable, long-term strategies
"""
    
    async def handle_request(self, user_message: str, state: ConversationState) -> str:
        """Handle strategy planning requests"""
        try:
            user_context = state.get_user_context_summary()
            profile = state.user_profile
            
            if any(word in user_message.lower() for word in ["schedule", "calendar", "when", "frequency"]):
                return await self._handle_scheduling_request(user_message, state)
            elif any(word in user_message.lower() for word in ["categories", "topics", "themes", "ideas"]):
                return await self._handle_category_generation(user_message, state)
            else:
                return """I can help you develop a comprehensive LinkedIn content strategy! Here's what I can assist with:

**1. Content Scheduling**
   - Optimal posting times and frequency
   - Content calendar creation
   - Posting schedule planning

**2. Content Categories & Themes**
   - Industry-relevant topic categories
   - Content pillar development
   - Theme rotation strategies

**3. Strategy Recommendations**
   - Audience engagement tactics
   - Growth strategies
   - Performance optimization

What aspect of content strategy would you like to focus on first? You can also tell me about your goals (e.g., "increase engagement," "build thought leadership," "grow network") and I'll provide tailored recommendations!"""
                
        except Exception as e:
            return f"I'm sorry, I encountered an error: {str(e)}. Could you tell me more about your content strategy goals?"
    
    async def _handle_scheduling_request(self, user_message: str, state: ConversationState) -> str:
        """Handle post scheduling requests"""
        # Extract time period from message
        time_period = self._extract_time_period(user_message)
        
        if time_period and state.user_profile.general_persona_keywords:
            # Generate a basic schedule recommendation
            return f"""Here's a recommended posting schedule for {time_period}:

**Optimal Posting Times:**
- Tuesday-Thursday: 8-10 AM or 12-2 PM (EST)
- Avoid Mondays (busy) and Fridays (low engagement)

**Frequency Recommendations:**
- **Beginner**: 2-3 posts per week
- **Intermediate**: 3-5 posts per week  
- **Advanced**: 5-7 posts per week (with high-quality content)

**Content Mix (80/20 rule):**
- 80% value-driven content (insights, tips, industry news)
- 20% personal/company updates

Would you like me to create a specific content calendar with dates and topics? I can generate a detailed schedule if you provide:
- Start and end dates
- Preferred posting frequency
- Specific content themes or categories"""
        else:
            return """To create an optimal posting schedule for you, I'd like to know:

**Time Frame:**
- What period are you planning for? (e.g., next month, next quarter)
- Any specific start/end dates?

**Preferences:**
- How often would you like to post? (daily, 3x/week, etc.)
- Any days you prefer to avoid?
- Best times for your audience (if known)?

**Content Focus:**
- What topics or themes do you want to cover?
- Any specific goals (thought leadership, networking, sales)?

I can then create a detailed content calendar with suggested posting times and topics!"""
    
    async def _handle_category_generation(self, user_message: str, state: ConversationState) -> str:
        """Handle content category generation requests"""
        if state.user_profile.general_persona_keywords:
            try:
                tool_input = {
                    "persona_keywords": state.user_profile.general_persona_keywords,
                    "content_keywords": state.user_profile.content_persona_keywords,
                    "network_keywords": state.user_profile.network_persona_keywords
                }
                
                result = TOOL_MAP["generate_categories"].func(json.dumps(tool_input))
                
                return f"""Here are content categories tailored to your professional profile:

{result}

**How to Use These Categories:**
1. **Content Pillars**: Choose 3-4 categories as your main content themes
2. **Rotation**: Cycle through categories to maintain variety
3. **Planning**: Use categories for monthly/weekly content planning
4. **Inspiration**: Each category can generate multiple post ideas

Would you like me to:
- Expand on any specific category?
- Create a content calendar using these categories?
- Suggest specific post ideas for each category?"""
                
            except Exception as e:
                return f"I encountered an issue generating categories: {str(e)}. Could you tell me more about your industry and interests?"
        else:
            return """I'd love to generate relevant content categories for you! To create the most useful categories, could you share:

**Your Professional Focus:**
- Industry or field you work in
- Your role or expertise area
- Key topics you're passionate about

**Content Goals:**
- What do you want to be known for?
- What value do you want to provide your audience?
- Any specific themes you want to cover?

Based on this information, I'll generate 8-10 content categories that you can use for consistent, strategic posting!"""
    
    def _extract_time_period(self, message: str) -> Optional[str]:
        """Extract time period from message"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["week", "weekly"]):
            return "this week"
        elif any(word in message_lower for word in ["month", "monthly"]):
            return "this month"
        elif any(word in message_lower for word in ["quarter", "quarterly"]):
            return "this quarter"
        elif any(word in message_lower for word in ["year", "yearly", "annual"]):
            return "this year"
        
        return None

class GeneralChatAgent:
    """Handles general conversation and fallback scenarios"""
    
    def __init__(self):
        self.llm = get_llm()
        
        self.system_prompt = """You are a friendly and helpful LinkedIn content creation assistant. You provide general assistance and handle conversational interactions.

Your role:
- Welcome new users and explain capabilities
- Handle general questions about LinkedIn content creation
- Provide guidance when users are unsure what they need
- Maintain a helpful and professional tone

VOCABULARY REQUIREMENTS:
- Use simple, clear words that everyone can understand
- Avoid complex or fancy vocabulary - choose common, everyday words instead
- Write at a level that is easy for all professionals to read and understand
- Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')
- Focus on clarity and understanding over sounding sophisticated
- Make content accessible to professionals of all backgrounds and education levels

User Context: {user_context}

Guidelines:
1. Be welcoming and friendly
2. Clearly explain available services
3. Ask helpful questions to understand user needs
4. Provide useful tips and general advice
5. Guide users toward specific agents when appropriate
"""
    
    async def handle_request(self, user_message: str, state: ConversationState) -> str:
        """Handle general chat and fallback requests"""
        try:
            user_context = state.get_user_context_summary()
            message_lower = user_message.lower()
            
            # Handle common greetings and questions
            if any(word in message_lower for word in ["hello", "hi", "hey", "greetings"]):
                return self._get_welcome_message(state)
            
            elif any(phrase in message_lower for phrase in ["what can you do", "help me", "capabilities", "features"]):
                return self._get_capabilities_message()
            
            elif any(phrase in message_lower for phrase in ["how to", "how do i", "help with"]):
                return self._get_guidance_message(user_message)
            
            elif any(word in message_lower for word in ["thanks", "thank you", "appreciate"]):
                return "You're very welcome! I'm here whenever you need help with LinkedIn content creation. What would you like to work on next?"
            
            else:
                # Use LLM for more complex conversational responses
                messages = [
                    SystemMessage(content=self.system_prompt.format(user_context=user_context)),
                    HumanMessage(content=user_message)
                ]
                
                response = await self.llm.ainvoke(messages)
                return response.content
                
        except Exception as e:
            return f"I'm here to help! While I encountered a small technical issue ({str(e)}), I can still assist you with LinkedIn content creation. What would you like to work on?"
    
    def _get_welcome_message(self, state: ConversationState) -> str:
        """Generate a personalized welcome message"""
        if state.user_profile.general_persona_keywords:
            return f"""Hello! Great to see you again. I remember you're in {', '.join(state.user_profile.general_persona_keywords[:2])}. How can I help you with your LinkedIn content today?

I can help you:
- Create new posts
- Find trending news for content inspiration  
- Modify existing content
- Plan your content strategy
- Analyze your professional profile

What would you like to work on?"""
        else:
            return """Hello! I'm your LinkedIn content creation assistant. I'm here to help you create engaging, professional content for your LinkedIn presence.

Here's what I can do for you:

🎯 **Create Content** - Generate original LinkedIn posts tailored to your professional brand
📰 **Find News & Trends** - Discover relevant industry news for content inspiration
✏️ **Edit & Improve** - Modify existing content to improve tone, style, or engagement
📊 **Strategy Planning** - Develop content calendars and posting strategies
👤 **Profile Analysis** - Analyze and optimize your LinkedIn profile

What would you like to start with? Just tell me what's on your mind!"""
    
    def _get_capabilities_message(self) -> str:
        """Provide detailed capabilities overview"""
        return """I'm a comprehensive LinkedIn content creation assistant with several specialized capabilities:

## 🎯 Content Creation
- Generate original posts based on your professional brand
- Create content for different tones (professional, casual, enthusiastic)
- Adapt content for various audiences and industries
- Generate multiple post variations

## 📰 News & Trend Discovery  
- Find trending news in your industry
- Discover content inspiration from current events
- Get article summaries and insights
- Suggest content angles based on trends

## ✏️ Content Editing & Improvement
- Modify tone, style, and length of existing posts
- Add emojis and hashtags strategically
- Rewrite content for different audiences
- Provide editing suggestions and improvements

## 📊 Strategy & Planning
- Create content calendars and posting schedules
- Generate content categories and themes
- Provide timing and frequency recommendations
- Develop long-term content strategies

## 👤 Profile Optimization
- Analyze LinkedIn profiles for optimization opportunities
- Generate professional persona keywords
- Score profiles and provide improvement suggestions
- Create content strategies based on your professional identity

Just tell me what you'd like help with, and I'll guide you through the process!"""
    
    def _get_guidance_message(self, user_message: str) -> str:
        """Provide guidance based on the user's question"""
        message_lower = user_message.lower()
        
        if "post" in message_lower or "content" in message_lower:
            return """Here are some ways I can help you with LinkedIn posts:

**Creating New Content:**
- "Help me write a post about [topic]"
- "Create a post for my industry"
- "Generate content about my recent achievement"

**Improving Existing Content:**
- "Make this post more professional: [paste content]"
- "Add emojis to this post"
- "Rewrite this for a technical audience"

**Content Strategy:**
- "Plan my content for next month"
- "What should I post about in my industry?"
- "Create a posting schedule"

What specific aspect would you like help with?"""
        
        elif "news" in message_lower or "trend" in message_lower:
            return """I can help you discover relevant news and trends for content inspiration:

**Finding Industry News:**
- "What's trending in [your industry]?"
- "Find recent news about [specific topic]"
- "Show me content inspiration for this week"

**Content Ideas from News:**
- I'll provide news summaries and suggest how to turn them into LinkedIn posts
- Get insights on industry developments
- Find discussion topics for engaging posts

Just tell me your industry or areas of interest, and I'll find relevant news for you!"""
        
        else:
            return """I'm here to help with all aspects of LinkedIn content creation! Here are some ways to get started:

**Quick Commands:**
- "Create a post about [topic]"
- "Find trending news in [industry]"
- "Help me plan my content strategy"
- "Analyze my LinkedIn profile"

**Or just describe what you need:**
- What you want to achieve on LinkedIn
- What challenges you're facing with content
- What type of help you're looking for

I'll understand your needs and guide you to the right solution. What would you like to work on?"""


# Agent Registry
AGENTS = {
    "intent_router": IntentRouterAgent(),
    "content_creation": ContentCreationAgent(),
    "content_modification": ContentModificationAgent(), 
    "news_discovery": NewsDiscoveryAgent(),
    "profile_analysis": ProfileAnalysisAgent(),
    "strategy_planning": StrategyPlanningAgent(),
    "general_chat": GeneralChatAgent()
}
