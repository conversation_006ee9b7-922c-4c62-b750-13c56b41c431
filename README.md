# LinkedIn Content Creation Platform

A sophisticated AI-powered platform for generating personalized LinkedIn content using Google's Vertex AI (Gemini 2.0 Flash) and advanced agentic architecture for trending news discovery.

## 🚀 Features

### Core Functionality
- **AI-Powered Content Generation**: Create personalized LinkedIn posts using Google's Gemini 2.0 Flash model
- **Persona-Based Content**: Generate content tailored to specific professional personas and interests
- **Trending News Discovery**: Advanced agentic system for finding and summarizing relevant industry news
- **Content Modification**: Modify existing content with different tones, styles, and lengths
- **Hashtag Generation**: Generate relevant hashtags for posts
- **Feed Analysis**: Analyze and categorize LinkedIn feed posts
- **Comment Suggestions**: Generate contextual comments for posts

### Advanced Features
- **Agentic Trending News**: Multi-agent system with Category Generator, News Retrieval, and Fallback agents
- **Smart Content Categorization**: Automatic categorization of content based on professional interests
- **Real-time News Summarization**: Fetch and summarize articles from multiple sources
- **Professional Persona Generation**: Create detailed professional personas from user profiles
- **Content Scheduling**: Schedule posts with automated generation

## 🏗️ Architecture

### Agentic System Components
- **SupervisorAgent**: Coordinates all sub-agents and manages workflow
- **CategoryGeneratorAgent**: Generates relevant news categories based on persona
- **NewsRetrievalAgent**: Fetches and validates news articles from Tavily API
- **FallbackAgent**: Provides alternative news sources when primary retrieval fails

### Technology Stack
- **Backend**: FastAPI with async/await support
- **AI Model**: Google Vertex AI (Gemini 2.0 Flash)
- **News API**: Tavily API for news discovery
- **Containerization**: Docker with multi-stage builds
- **Cloud Deployment**: Google Cloud Platform (Cloud Run)
- **Caching**: In-memory caching with TTL support

## 📋 Prerequisites

- Python 3.11+
- Docker and Docker Compose
- Google Cloud Platform account
- Tavily API key (for news discovery)

## 🔧 Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd V2-Linkedin-Content-Creation
```

### 2. Set Up Environment Variables
Create a `.env` file in the root directory:
```bash
# Google Cloud Configuration
PROJECT_ID=your-gcp-project-id
LOCATION=us-central1
MODEL_NAME=gemini-2.0-flash

# API Keys
TAVILY_API_KEY=your-tavily-api-key

# Service Account (for local development)
SERVICE_ACCOUNT_KEY=path/to/your/service-account-key.json
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Set Up Google Cloud Authentication
For local development, you'll need a service account key:
1. Go to Google Cloud Console
2. Create a service account with Vertex AI permissions
3. Download the JSON key file
4. Place it in `app/utils/google/` directory
5. Update the path in your `.env` file

## 🚀 Quick Start

### Local Development
```bash
# Run with Docker Compose
docker-compose up --build

# Or run directly with Python
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Production Deployment
```bash
# Build and run with Docker
docker build -t linkedin-content-api .
docker run -p 8000:8000 linkedin-content-api
```

## 📚 API Endpoints

### Core Content Generation
- `POST /create-post` - Generate a single LinkedIn post
- `POST /schedule-posts` - Schedule multiple posts over time
- `POST /modify-content` - Modify existing content
- `POST /suggest-posts` - Generate multiple post suggestions

### Persona Management
- `POST /create-general-persona` - Create professional persona from profile
- `POST /generate-content-categories` - Generate content categories

### News and Trends
- `POST /trending-news` - Get trending news using agentic system
- `POST /popular-posts-hashtags` - Get popular hashtags

### Feed Analysis
- `POST /todays-feed` - Analyze and categorize LinkedIn feed
- `POST /suggest-comment-to-post` - Generate comment suggestions

### Utility Endpoints
- `GET /health` - Health check
- `GET /performance` - Performance statistics
- `POST /generate-url` - Generate URLs for content

## 🔒 Security Considerations

### Environment Variables
- **Never commit API keys or credentials to version control**
- Use `.env` files for local development
- Use Google Cloud Secret Manager for production
- Rotate API keys regularly

### Service Account Security
- Use least-privilege principle for service accounts
- Store service account keys securely
- Use workload identity in production

### API Security
- Implement rate limiting for production
- Use HTTPS in production
- Validate all input data
- Implement proper error handling

## 🐳 Docker Deployment

### Local Docker Setup
```bash
# Build the image
docker build -t linkedin-content-api .

# Run the container
docker run -p 8000:8000 \
  -e PROJECT_ID=your-project-id \
  -e SERPAPI_KEY=your-api-key \
  linkedin-content-api
```

### Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## ☁️ Google Cloud Deployment

### Prerequisites
1. Google Cloud SDK installed
2. Project with Vertex AI API enabled
3. Service account with appropriate permissions

### Deploy to Cloud Run
```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT_ID/linkedin-content-api

# Deploy to Cloud Run
gcloud run deploy linkedin-content-api \
  --image gcr.io/PROJECT_ID/linkedin-content-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Environment Variables in Cloud Run
Set these in the Cloud Run console:
- `PROJECT_ID`
- `SERPAPI_KEY`
- `MODEL_NAME`

## 📊 Performance Optimization

### Caching Strategy
- In-memory caching for article summaries (1-hour TTL)
- Consider Redis for distributed deployments
- Cache frequently requested content

### Rate Limiting
- Built-in delays between API calls
- Exponential backoff for retries
- Configurable rate limits

### Async Processing
- All I/O operations are async
- Parallel processing where possible
- Non-blocking operations

## 🧪 Testing

### Run Tests
```bash
# Run the test suite
python -m pytest

# Run specific test file
python test_persona_feed.py
```

### Test Coverage
- Unit tests for core functionality
- Integration tests for API endpoints
- Performance tests for agentic system

## 📝 Configuration

### Model Configuration
```python
# config.py
MODEL_NAME = "gemini-2.0-flash"
PROJECT_ID = "your-project-id"
LOCATION = "us-central1"
```

### Agentic System Configuration
```python
# Production settings
MAX_RETRIES = 3
REQUEST_TIMEOUT = 15
RATE_LIMIT_DELAY = 0.2
CACHE_TTL = 3600  # 1 hour
```

## 🔧 Troubleshooting

### Common Issues

1. **Service Account Key Not Found**
   - Ensure the key file is in the correct location
   - Check file permissions
   - Verify the path in config.py

2. **SERP API Errors**
   - Verify API key is valid
   - Check rate limits
   - Ensure proper error handling

3. **Model Initialization Failures**
   - Verify Google Cloud credentials
   - Check Vertex AI API is enabled
   - Ensure proper project configuration

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
uvicorn app.main:app --reload --log-level debug
```

## 📈 Monitoring and Logging

### Logging Configuration
- Structured logging with different levels
- Error tracking and monitoring
- Performance metrics collection

### Health Checks
- `/health` endpoint for basic health
- `/performance` for detailed metrics
- Automatic monitoring in Cloud Run

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style
- Follow PEP 8 guidelines
- Use type hints
- Add docstrings for functions
- Keep functions small and focused

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
1. Check the troubleshooting section
2. Review the API documentation
3. Open an issue on GitHub
4. Contact the development team

## 🔄 Version History

- **v2.0**: Agentic trending news system, improved content generation
- **v1.0**: Basic content generation and persona management

---

**Note**: This is a production-ready system with advanced AI capabilities. Ensure proper security measures are in place before deployment. 