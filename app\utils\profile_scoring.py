import json
from app.utils.model_initializer import model
from datetime import datetime

EXPERIENCE_CATEGORIES = {
    "Entry Level": (0, 1),
    "Junior": (1, 3),
    "Mid-Level": (3, 7),
    "Senior": (7, 15),
    "Expert": (15, 100)
}

CEO_TITLES = ["CEO", "CTO", "CF<PERSON>", "CO<PERSON>", "C<PERSON>", "<PERSON><PERSON>"]

def calculate_duration(start_date, end_date):
    # Extract year from date string (handles YYYY-MM-DD format)
    try:
        if start_date and start_date != '0':
            # Handle YYYY-MM-DD format
            if '-' in start_date:
                start_year = int(start_date.split('-')[0])
            else:
                # Fallback for other format
                start_year = int(start_date.split()[-1])
        else:
            start_year = 0
    except (ValueError, AttributeError):
        start_year = 0

    if end_date and end_date.lower() == 'present':
        end_year = datetime.now().year
    else:
        try:
            if end_date and end_date != '0':
                # Handle YYYY-MM-DD formats
                if '-' in end_date:
                    end_year = int(end_date.split('-')[0])
                else:
                    # Fallback for other formats
                    end_year = int(end_date.split()[-1])
            else:
                end_year = 0
        except (ValueError, AttributeError):
            end_year = 0

    return max(0, end_year - start_year)

def categorize_experience(work_experience):
    total_years = sum(calculate_duration(job.get("startDate", "0"), job.get("endDate", "0")) for job in work_experience)
    for category, (min_exp, max_exp) in EXPERIENCE_CATEGORIES.items():
        if min_exp <= total_years < max_exp:
            return category

    for job in work_experience:
        if job.get("position") in CEO_TITLES:
            return "Upper Management"

    return "Unknown"

def check_missing_attributes(profile_data):
    missing_attributes_suggestions = []

    attributes_to_check = {
        "name": "Name",
        "headline": "Headline",
        "summary": "Summary",
        "work_experience": "Work Experience",
        "current_role": "Current Role",
        "organizations": "Organization",
        "industry": "Industry",
        "city": "City",
        "country": "Country",
        "skills": "Skills",
        "certifications": "Certifications",
        "languages": "Languages",
        "education": "Education (Degrees, Institutions, Years of Study)"
    }

    for attribute_key, attribute_name in attributes_to_check.items():
        if not profile_data.get(attribute_key):
            missing_attributes_suggestions.append(f"- Add a '{attribute_name}' to make your profile more complete.")

    missing_attributes_text = "\n".join(missing_attributes_suggestions) if missing_attributes_suggestions else "Profile attributes are reasonably complete."
    return missing_attributes_text, missing_attributes_suggestions

def score_profile(profile_data):
    experience_category = categorize_experience(profile_data.get("work_experience", []))
    missing_attributes_text, missing_attributes_suggestions = check_missing_attributes(profile_data)

    score_prompt = f"""
    **Objective:** Evaluate the provided LinkedIn profile. Your response MUST be a single JSON object.

    **Step 1: Analyze the Profile in Detail**
    First, carefully review all the provided attributes of the LinkedIn profile below. Pay close attention to the 'Experience Level' ({experience_category}) and 'Industry' ({profile_data.get('industry', 'N/A')}) to ensure your evaluation is relevant.

    **Profile Data:**
    - Name: {profile_data.get('name', 'N/A')}
    - Headline: {profile_data.get('headline', 'N/A')}
    - Summary: {profile_data.get('summary', 'N/A')}
    - Work Experience: {profile_data.get('work_experience', 'N/A')}
    - Current Role: {profile_data.get('current_role', 'N/A')}
    - Industry: {profile_data.get('industry', 'N/A')}
    - Skills: {profile_data.get('skills', [])}
    - Education: {profile_data.get('education', [])}
    - Others: (Certifications, Languages, Location, etc.)
    - Experience Level: {experience_category}
    - Profile Completeness Check: {missing_attributes_text}

    **Step 2: Score the Profile Based on Specific Criteria**
    Assign a score from 0 to 100 for each category based on the following criteria. The overall score should be a weighted average.

    - **Headline Score:**
      - Criteria: Is it concise? Does it clearly state the current role and key value proposition? Does it include relevant keywords for their industry? (e.g., "Senior Software Engineer at Tech Corp | Cloud, DevOps, Python").
    - **Summary Score:**
      - Criteria: Is it written in the first person? Does it narrate a professional story? Does it highlight key achievements with metrics (e.g., "Increased sales by 20%")? Does it end with a clear call to action?
    - **Work Experience Score:**
      - Criteria: Are roles described with bullet points? Do bullets start with action verbs? Are responsibilities and, more importantly, achievements quantified where possible?
    - **Education Score:**
      - Criteria: Is the degree, institution, and duration listed? Is it relevant to the user's career path?
    - **Others Score:**
      - Criteria: How complete are the remaining sections? Does the skills section list at least 5-10 relevant skills? Are there any certifications or languages that add value?

    **Step 3: Generate Actionable and Personalized Suggestions**
    Based on your analysis, provide specific, actionable improvement suggestions. The suggestions should be tailored to the user's experience level. For example, a suggestion for an "Entry Level" profile should be different from one for a "Senior" level profile.

    **Step 4: Format the Output**
    Return your complete analysis as a single JSON object. Do not include any text or formatting like "```json" before or after the JSON object.

    {{
    "overall_score": 0-100,
    "headline_score": 0-100,
    "summary_score": 0-100,
    "work_experience_score": 0-100,
    "education_score": 0-100,
    "others_score": 0-100,
    "evaluation": "Brief analysis of the profile's strengths and weaknesses based on your step-by-step reasoning.",
    "suggestions": {{
        "headline": "Improvement suggestions for headline based on the scoring criteria.",
        "summary": "Improvement suggestions for summary based on the scoring criteria.",
        "work_experience": "Suggestions for work experience section based on the scoring criteria.",
        "education": "Suggestions for education section.",
        "skills": "Recommendations for skills section, suggesting relevant skills if missing.",
        "general": "Overall suggestions to enhance profile completeness and impact."
    }}
    }}
    """

    try:
        response = model.generate_content(score_prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        json_response = json.loads(response_text)
        return json_response
    except Exception as e:
        return {"error": f"Error scoring profile: {e}"}