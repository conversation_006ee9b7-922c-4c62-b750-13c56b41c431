"""
LangGraph-based Chatbot Orchestrator
Manages conversation flow and agent routing using LangGraph state management.
"""

from typing import Dict, Any, List, Optional, Annotated
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from dataclasses import dataclass
import asyncio

from app.chatbot.agents import AGENTS
from app.chatbot.state import ConversationState, state_manager

@dataclass
class ChatbotState:
    """State for the LangGraph chatbot workflow"""
    messages: Annotated[List[BaseMessage], add_messages]
    session_id: str
    current_intent: Optional[str] = None
    agent_response: Optional[str] = None
    conversation_state: Optional[ConversationState] = None
    user_context: Optional[str] = None
    routing_confidence: float = 0.0
    needs_clarification: bool = False
    error_message: Optional[str] = None

class ChatbotOrchestrator:
    """Main orchestrator for the agentic chatbot using LangGraph"""
    
    def __init__(self):
        self.agents = AGENTS
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow"""
        
        # Create the state graph
        workflow = StateGraph(ChatbotState)
        
        # Add nodes for each step in the conversation flow
        workflow.add_node("load_state", self._load_conversation_state)
        workflow.add_node("classify_intent", self._classify_intent)
        workflow.add_node("route_to_agent", self._route_to_agent)
        workflow.add_node("handle_low_confidence", self._handle_low_confidence)
        workflow.add_node("save_state", self._save_conversation_state)
        workflow.add_node("error_handler", self._handle_error)
        
        # Define the conversation flow
        workflow.set_entry_point("load_state")
        
        # Flow: load_state -> classify_intent -> route based on confidence
        workflow.add_edge("load_state", "classify_intent")
        
        # Conditional routing based on intent classification confidence
        workflow.add_conditional_edges(
            "classify_intent",
            self._should_route_to_agent,
            {
                "route": "route_to_agent",
                "clarify": "handle_low_confidence",
                "error": "error_handler"
            }
        )
        
        # Both agent routing and clarification lead to state saving
        workflow.add_edge("route_to_agent", "save_state")
        workflow.add_edge("handle_low_confidence", "save_state")
        workflow.add_edge("error_handler", "save_state")
        
        # End after saving state
        workflow.add_edge("save_state", END)
        
        return workflow.compile()
    
    async def _load_conversation_state(self, state: ChatbotState) -> ChatbotState:
        """Load conversation state for the session"""
        try:
            # Get or create conversation state
            conversation_state = state_manager.get_state(state.session_id)
            
            # Add the latest user message to conversation history
            if state.messages and isinstance(state.messages[-1], HumanMessage):
                conversation_state.add_message(state.messages[-1])
            
            # Update state
            state.conversation_state = conversation_state
            state.user_context = conversation_state.get_user_context_summary()
            
            return state
            
        except Exception as e:
            state.error_message = f"Error loading conversation state: {str(e)}"
            return state
    
    async def _classify_intent(self, state: ChatbotState) -> ChatbotState:
        """Classify user intent using the Intent Router Agent"""
        try:
            if not state.messages:
                state.error_message = "No messages to classify"
                return state
            
            # Get the latest user message
            user_message = state.messages[-1].content if state.messages else ""
            
            # Classify intent
            intent_agent = self.agents["intent_router"]
            intent, confidence = await intent_agent.classify_intent(
                user_message, 
                state.conversation_state
            )
            
            state.current_intent = intent
            state.routing_confidence = confidence
            
            # Update conversation state
            if state.conversation_state:
                state.conversation_state.set_current_intent(intent, "intent_router")
            
            return state
            
        except Exception as e:
            state.error_message = f"Error classifying intent: {str(e)}"
            return state
    
    def _should_route_to_agent(self, state: ChatbotState) -> str:
        """Determine whether to route to agent or handle low confidence"""
        if state.error_message:
            return "error"
        elif state.routing_confidence >= 0.6:  # High confidence threshold
            return "route"
        else:
            return "clarify"
    
    async def _route_to_agent(self, state: ChatbotState) -> ChatbotState:
        """Route to the appropriate specialist agent"""
        try:
            if not state.current_intent or state.current_intent not in self.agents:
                # Fallback to general chat
                state.current_intent = "general_chat"
            
            # Get the appropriate agent
            agent = self.agents[state.current_intent]
            
            # Get user message
            user_message = state.messages[-1].content if state.messages else ""
            
            # Handle the request with the appropriate agent
            response = await agent.handle_request(user_message, state.conversation_state)
            
            state.agent_response = response
            
            # Add AI response to conversation history
            if state.conversation_state:
                state.conversation_state.add_ai_message(response)
            
            return state
            
        except Exception as e:
            state.error_message = f"Error routing to agent: {str(e)}"
            return state
    
    async def _handle_low_confidence(self, state: ChatbotState) -> ChatbotState:
        """Handle low confidence intent classification"""
        try:
            # Use general chat agent for clarification
            general_agent = self.agents["general_chat"]
            user_message = state.messages[-1].content if state.messages else ""
            
            # Add a clarifying prefix to help the general agent understand
            clarification_prompt = f"""I'm not entirely sure what you're looking for. Here's what you said: "{user_message}"

Let me help you find the right assistance. {await general_agent.handle_request(user_message, state.conversation_state)}"""
            
            state.agent_response = clarification_prompt
            state.needs_clarification = True
            
            # Add AI response to conversation history
            if state.conversation_state:
                state.conversation_state.add_ai_message(clarification_prompt)
            
            return state
            
        except Exception as e:
            state.error_message = f"Error handling low confidence: {str(e)}"
            return state
    
    async def _handle_error(self, state: ChatbotState) -> ChatbotState:
        """Handle errors gracefully"""
        error_message = state.error_message or "An unexpected error occurred."
        
        fallback_response = f"""I apologize, but I encountered an issue: {error_message}

However, I'm still here to help! Here's what I can assist you with:

🎯 **Create LinkedIn Posts** - Generate engaging content for your professional brand
📰 **Find Industry News** - Discover trending topics for content inspiration  
✏️ **Edit Content** - Improve existing posts with better tone and style
📊 **Plan Strategy** - Develop content calendars and posting schedules
👤 **Analyze Profiles** - Optimize your LinkedIn presence

What would you like to work on?"""
        
        state.agent_response = fallback_response
        
        # Add AI response to conversation history
        if state.conversation_state:
            state.conversation_state.add_ai_message(fallback_response)
        
        return state
    
    async def _save_conversation_state(self, state: ChatbotState) -> ChatbotState:
        """Save the updated conversation state"""
        try:
            if state.conversation_state:
                # Update the global state manager
                state_manager.update_state(state.session_id, state.conversation_state)
            
            return state
            
        except Exception as e:
            # Log error but don't fail the entire conversation
            print(f"Error saving conversation state: {str(e)}")
            return state
    
    async def chat(self, message: str, session_id: str) -> str:
        """Main chat interface"""
        try:
            # Create initial state
            initial_state = ChatbotState(
                messages=[HumanMessage(content=message)],
                session_id=session_id
            )
            
            # Run the workflow
            final_state = await self.graph.ainvoke(initial_state)
            
            # Return the agent response
            return final_state.get("agent_response", "I'm sorry, I couldn't process your request. Please try again.")
            
        except Exception as e:
            return f"""I apologize, but I encountered an unexpected error: {str(e)}

I'm still here to help you with LinkedIn content creation! Here's what I can do:
- Create engaging LinkedIn posts
- Find trending industry news
- Edit and improve existing content
- Plan content strategies
- Analyze LinkedIn profiles

What would you like to work on?"""
    
    async def get_conversation_history(self, session_id: str) -> List[Dict[str, Any]]:
        """Get conversation history for a session"""
        try:
            conversation_state = state_manager.get_state(session_id)
            
            history = []
            for message in conversation_state.messages:
                history.append({
                    "role": "user" if isinstance(message, HumanMessage) else "assistant",
                    "content": message.content,
                    "timestamp": getattr(message, 'timestamp', None)
                })
            
            return history
            
        except Exception as e:
            return [{"role": "system", "content": f"Error retrieving history: {str(e)}"}]
    
    async def clear_conversation(self, session_id: str) -> bool:
        """Clear conversation history for a session"""
        try:
            state_manager.clear_state(session_id)
            return True
        except Exception as e:
            print(f"Error clearing conversation: {str(e)}")
            return False
    
    async def update_user_profile(self, session_id: str, profile_updates: Dict[str, Any]) -> bool:
        """Update user profile information"""
        try:
            conversation_state = state_manager.get_state(session_id)
            conversation_state.update_user_profile(**profile_updates)
            state_manager.update_state(session_id, conversation_state)
            return True
        except Exception as e:
            print(f"Error updating user profile: {str(e)}")
            return False
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active conversation sessions"""
        return state_manager.get_active_sessions()

# Global orchestrator instance
chatbot_orchestrator = ChatbotOrchestrator()
