# post_management.py
from typing import List, Dict, Any, Optional
import random
from datetime import datetime, timedelta
import uuid
import re
import concurrent.futures
import threading
import logging
import functools # <-- ADDED for caching

# --- IMPROVED: Import the async version of fetch_related_url ---
from app.utils.post_creator import (
    generate_post_from_persona_keywords,
    fetch_related_url,
    generate_search_query_from_content,
    analyze_post_for_media,
    _parse_post_generation_response # <-- NEW: Import the centralized parser
)
from app.utils.model_initializer import model # Direct import for should_post_have_url

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


@functools.lru_cache(maxsize=128) # <-- OPTIMIZATION: Cache the results of this function
def get_category_framework_mapping(category: str) -> Dict[str, Any]:
    """
    AI-powered intelligent framework selection that analyzes any category semantically
    and selects the most appropriate LinkedIn post framework and style dynamically.

    Args:
        category: Any content category (e.g., "productivity hacks", "remote work challenges")

    Returns:
        Dictionary containing framework information and persona relevance
    """
    from app.utils.model_initializer import model

    # Define available frameworks (same as in post_creator.py)
    available_frameworks = [
        {
            "name": "General & Direct Post",
            "description": "A straightforward and clear post that gets directly to the point. Ideal for announcements, celebrations, and simple topics.",
            "best_for": ["Event", "Awareness", "Engagement"]
        },
        {
            "name": "AIDA",
            "description": "Get attention, build interest, create want, and ask for action.",
            "best_for": ["Awareness", "Engagement", "Branding"]
        },
        {
            "name": "PAS",
            "description": "Point out a problem, show why it hurts, and give the solution.",
            "best_for": ["Authority", "Informative"]
        },
        {
            "name": "Before-After-Bridge",
            "description": "Show the current situation, envision the ideal state, and then map out the steps to get there.",
            "best_for": ["Informative", "Authority"]
        },
        {
            "name": "Listicle",
            "description": "A clear list format with useful tips.",
            "best_for": ["Informative", "Engagement"]
        },
        {
            "name": "Question-led",
            "description": "Start with an interesting question and then give insights.",
            "best_for": ["Engagement", "Authority"]
        },
        {
            "name": "Data-Driven Persuasion",
            "description": "Start with surprising statistics, explain why they matter, and give one clear action step.",
            "best_for": ["Authority", "Informative"]
        },
        {
            "name": "Credible Spotlight",
            "description": "Highlight real people or organizations while connecting to personal values and bigger missions.",
            "best_for": ["Authority", "Branding"]
        },
        {
            "name": "Counterintuitive Leadership Truth",
            "description": "Challenge common beliefs with surprising insights and give better ways to do things.",
            "best_for": ["Authority", "Engagement"]
        }
    ]

    # Create framework list for AI prompt
    framework_list = "\n".join([
        f"• **{fw['name']}**: {fw['description']} (Best for: {', '.join(fw['best_for'])})"
        for fw in available_frameworks
    ])

    # --- MODIFIED: The prompt now asks the AI to select a post style ---
    analysis_prompt = f"""
You are an expert LinkedIn content strategist. Analyze the following content category and select the most appropriate framework and post style for a LinkedIn post.

**Category to Analyze:** "{category}"

**Available Frameworks:**
{framework_list}

**Analysis Instructions:**
1. Analyze the category's content type (educational, personal, news, motivational, etc.) and its primary purpose (inform, engage, inspire, etc.).
2. Select the best framework from the list provided.
3. Select the most appropriate post style. Choose from styles like: "Informative", "Inspirational", "Narrative/Storytelling", "Provocative", "Educational", "Analytical", "Humorous", "Question-based".

**Persona Integration Guidelines:**
- **Deep**: Personal stories, achievements, insights where the author's experience is central.
- **Contextual**: Tips, advice, industry content where background adds credibility.
- **Minimal**: Questions, news, trends where focus is on the content, not the author.

Return your analysis in this exact JSON format:
{{
    "selected_framework": "Framework Name",
    "post_style": "Style Name",
    "reasoning": "Brief explanation of why this framework and style are best for this category",
    "persona_relevance": "deep|contextual|minimal",
    "persona_integration_style": "narrative_based|expertise_based|experience_based|perspective_based|curiosity_based|inspirational_based|announcement_based",
    "content_characteristics": "Brief description of what makes this category unique"
}}

Respond with ONLY the JSON object, no additional text.
"""

    try:
        response = model.generate_content(analysis_prompt, use_cache=False, temperature=0.3)
        analysis_text = response.text.strip()

        # Clean up the response to extract JSON
        if "```json" in analysis_text:
            parts = analysis_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    analysis_text = sub_parts.strip()
        elif "```" in analysis_text:
            # Handle cases where JSON is wrapped in generic code blocks
            parts = analysis_text.split("```", 1)
            if len(parts) > 1:
                sub_parts = parts.split("```", 1)
                if len(sub_parts) > 0:
                    analysis_text = sub_parts[0].strip()

        import json
        analysis_result = json.loads(analysis_text)

        # Validate that the selected framework exists
        selected_framework_name = analysis_result.get("selected_framework", "General & Direct Post")
        framework_names = [fw["name"] for fw in available_frameworks]
        if selected_framework_name not in framework_names:
            logging.warning(f"AI selected unknown framework '{selected_framework_name}', using fallback")
            selected_framework_name = "General & Direct Post"

        # --- MODIFICATION: Find the full framework object ---
        selected_framework_details = next(
            (fw for fw in available_frameworks if fw["name"] == selected_framework_name),
            available_frameworks[0] # Fallback to the first framework
        )

        return {
            "framework_details": selected_framework_details, # <-- RETURN THE FULL OBJECT
            "post_style": analysis_result.get("post_style", "Informative"),
            "reason": analysis_result.get("reasoning", f"AI-selected framework for {category} content"),
            "persona_relevance": analysis_result.get("persona_relevance", "contextual"),
            "natural_persona_integration": analysis_result.get("persona_integration_style", "balanced"),
            "content_characteristics": analysis_result.get("content_characteristics", "")
        }

    except Exception as e:
        logging.error(f"Error in AI framework selection for category '{category}': {str(e)}")
        # Fallback to intelligent default based on simple keyword analysis
        fallback_selection = _get_fallback_framework_mapping(category)
        fallback_framework_details = next(
            (fw for fw in available_frameworks if fw["name"] == fallback_selection["framework"]),
            available_frameworks[0]
        )
        fallback_selection["framework_details"] = fallback_framework_details
        fallback_selection["post_style"] = "Informative"
        return fallback_selection


def _get_fallback_framework_mapping(category: str) -> Dict[str, Any]:
    """
    Fallback framework selection using simple keyword analysis when AI fails.

    Args:
        category: Content category

    Returns:
        Dictionary with fallback framework selection
    """
    category_lower = category.lower()

    # Simple keyword-based fallback logic
    if any(word in category_lower for word in ["tip", "how", "step", "guide", "list", "way"]):
        return {
            "framework": "Listicle",
            "reason": "Category suggests structured, actionable content best suited for list format",
            "persona_relevance": "contextual",
            "natural_persona_integration": "expertise_based"
        }
    elif any(word in category_lower for word in ["story", "personal", "journey", "experience", "achievement"]):
        return {
            "framework": "General & Direct Post",
            "reason": "Category suggests personal narrative content",
            "persona_relevance": "deep",
            "natural_persona_integration": "narrative_based"
        }
    elif any(word in category_lower for word in ["question", "ask", "poll", "discuss", "opinion"]):
        return {
            "framework": "Question-led",
            "reason": "Category suggests engagement-focused content",
            "persona_relevance": "minimal",
            "natural_persona_integration": "curiosity_based"
        }
    elif any(word in category_lower for word in ["news", "trend", "update", "report", "analysis"]):
        return {
            "framework": "PAS",
            "reason": "Category suggests commentary or analysis content",
            "persona_relevance": "minimal",
            "natural_persona_integration": "perspective_based"
        }
    elif any(word in category_lower for word in ["insight", "truth", "reality", "myth", "belief"]):
        return {
            "framework": "Counterintuitive Leadership Truth",
            "reason": "Category suggests thought-provoking content that challenges assumptions",
            "persona_relevance": "contextual",
            "natural_persona_integration": "perspective_based"
        }
    else:
        return {
            "framework": "General & Direct Post",
            "reason": "Versatile framework suitable for general professional content",
            "persona_relevance": "contextual",
            "natural_persona_integration": "balanced"
        }


def determine_persona_integration_strategy(category: str, persona_relevance: str, persona_data: Dict, integration_style: str = "balanced") -> Dict[str, Any]:
    """
    Determines how persona information should be naturally integrated based on AI analysis.

    Args:
        category: Content category
        persona_relevance: Level of persona relevance (deep/contextual/minimal) from AI analysis
        persona_data: User's persona information
        integration_style: Style of integration from AI analysis

    Returns:
        Dictionary with integration strategy and instructions
    """
    general_keywords = persona_data.get('general_persona_keywords', [])

    # Create dynamic instructions based on AI-determined integration style
    style_instructions = {
        "narrative_based": {
            "instruction": f"Naturally weave the user's background into the narrative. Select 1-2 most relevant aspects from their experience ({', '.join(general_keywords[:3])}) that directly relate to the {category}. Let the experience emerge through storytelling rather than announcing credentials.",
            "avoid_phrases": ["As a", "With my experience as", "Being a", "In my role as"],
            "preferred_approach": "Let expertise show through personal narrative and insights"
        },
        "expertise_based": {
            "instruction": f"Reference background only when it directly supports the {category} content's credibility. Use natural phrases like 'In my experience...' or 'I've found that...' rather than announcing titles. Focus on the value provided, not the credentials held.",
            "avoid_phrases": ["As a senior", "With X years of experience", "Being an expert in"],
            "preferred_approach": "Lead with valuable insight, support with relevant experience when natural"
        },
        "experience_based": {
            "instruction": f"Draw from relevant professional experience to support the {category} content. Share lessons learned or observations made, but avoid resume-style introductions. Let wisdom speak louder than titles.",
            "avoid_phrases": ["As someone with", "Having worked as", "In my capacity as"],
            "preferred_approach": "Share experiential wisdom without credential announcements"
        },
        "perspective_based": {
            "instruction": f"Offer a professional perspective on the {category} topic. Only mention background if it's essential for context. Focus on the viewpoint and analysis rather than who's providing it.",
            "avoid_phrases": ["As a professional", "From my position as", "Speaking as a"],
            "preferred_approach": "Lead with perspective and analysis, minimize personal references"
        },
        "curiosity_based": {
            "instruction": f"Focus entirely on engaging the audience with the {category} content. Write from a curious, questioning perspective that invites participation. Avoid personal references unless they're essential to the question being posed.",
            "avoid_phrases": ["As someone who", "In my experience with", "Having dealt with"],
            "preferred_approach": "Universal curiosity that resonates with broad professional audience"
        },
        "inspirational_based": {
            "instruction": f"Create inspiring content about {category} that motivates others. Use personal experience sparingly and only when it serves the inspirational message. Focus on uplifting the audience rather than showcasing credentials.",
            "avoid_phrases": ["As a successful", "Having achieved", "Through my journey as"],
            "preferred_approach": "Inspire through universal truths and relatable insights"
        },
        "announcement_based": {
            "instruction": f"Make clear, direct announcements about {category}. Include personal context only when it's relevant to the announcement. Keep the focus on the news or information being shared.",
            "avoid_phrases": ["As the", "In my role of", "Being responsible for"],
            "preferred_approach": "Clear, direct communication with minimal personal positioning"
        }
    }

    # Get the appropriate instruction set
    instruction_set = style_instructions.get(integration_style, style_instructions["expertise_based"])

    if persona_relevance == "deep":
        return {
            "should_include_persona": True,
            "integration_style": integration_style,
            "instruction": instruction_set["instruction"],
            "avoid_phrases": instruction_set["avoid_phrases"],
            "preferred_approach": instruction_set["preferred_approach"]
        }
    elif persona_relevance == "contextual":
        return {
            "should_include_persona": True,
            "integration_style": integration_style,
            "instruction": instruction_set["instruction"] + " Use persona information selectively - only when it genuinely adds value to the content.",
            "avoid_phrases": instruction_set["avoid_phrases"],
            "preferred_approach": instruction_set["preferred_approach"] + " Be selective about when to include personal context."
        }
    else:  # minimal
        return {
            "should_include_persona": False,
            "integration_style": "perspective_only",
            "instruction": f"Focus entirely on the {category} content. Write from a general professional perspective. Only reference personal experience if it's absolutely essential to the point being made.",
            "avoid_phrases": ["As a", "In my role", "With my background", "Having experience in"],
            "preferred_approach": "Universal perspective that resonates with broad professional audience"
        }


def create_intelligent_category_prompt(category: str, persona_data: Dict, optimized_length: str, day_index: int) -> str:
    """
    Creates an intelligent prompt using AI-powered framework selection and natural persona integration.

    Args:
        category: Any content category (e.g., "productivity hacks", "remote work challenges")
        persona_data: User's persona information
        optimized_length: Post length (short/medium/long)
        day_index: Day number for uniqueness

    Returns:
        Intelligently crafted prompt string
    """
    # Get AI-powered framework mapping for this category
    framework_mapping = get_category_framework_mapping(category)
    # --- MODIFICATION: Extract details from the framework object ---
    framework_details = framework_mapping["framework_details"]
    framework_name = framework_details["name"]
    framework_description = framework_details["description"]
    persona_relevance = framework_mapping["persona_relevance"]
    integration_style = framework_mapping["natural_persona_integration"]

    # Determine persona integration strategy using AI analysis
    persona_strategy = determine_persona_integration_strategy(
        category, persona_relevance, persona_data, integration_style
    )

    # --- MODIFICATION: Build a more intelligent prompt ---
    base_prompt = (
        f"Write a {optimized_length} LinkedIn post about '{category}'. "
        f"IMPORTANT: Structure the content according to this principle: '{framework_description}'. "
        f"Do NOT mention the name of the framework (like '{framework_name}' or 'framework') in the post body."
    )

    # Add persona integration instructions
    if persona_strategy["should_include_persona"]:
        base_prompt += f" PERSONA INTEGRATION: {persona_strategy['instruction']}"

        # Add specific avoid phrases guidance
        if persona_strategy["avoid_phrases"]:
            avoid_list = "', '".join(persona_strategy["avoid_phrases"])
            base_prompt += f" AVOID these phrases: '{avoid_list}'."

        # Add preferred approach
        base_prompt += f" APPROACH: {persona_strategy['preferred_approach']}"
    else:
        base_prompt += f" PERSONA GUIDANCE: {persona_strategy['instruction']}"

    # Add content focus if available
    if persona_data.get('content_persona_keywords'):
        base_prompt += f" CONTENT FOCUS: Consider incorporating insights related to: {', '.join(persona_data['content_persona_keywords'][:3])}."

    # Add target audience if available
    if persona_data.get('network_persona_keywords'):
        base_prompt += f" TARGET AUDIENCE: {', '.join(persona_data['network_persona_keywords'][:2])}."

    # Add content characteristics if available from AI analysis
    if framework_mapping.get('content_characteristics'):
        base_prompt += f" CONTENT NOTES: {framework_mapping['content_characteristics']}"

    # Add uniqueness factor
    base_prompt += f" [Schedule Day {day_index} - ensure unique angle and approach for this specific category]"

    return base_prompt


def create_balanced_length_distribution(num_days: int, content_categories: List[str]) -> List[str]:
    """
    Create a balanced distribution of post lengths for scheduled posts.

    Args:
        num_days: Number of days to schedule posts for
        content_categories: List of content categories for context

    Returns:
        List of length assignments for each day
    """
    # Define length distribution strategy
    lengths = ["short", "medium", "long"]

    # Create balanced distribution patterns
    if num_days <= 7:
        # Weekly pattern: ensure variety
        base_pattern = ["short", "medium", "long", "medium", "short", "long", "medium"]
        length_distribution = (base_pattern * ((num_days // 7) + 1))[:num_days]
    elif num_days <= 30:
        # Monthly pattern: balanced with slight emphasis on medium
        # Ratio: 30% short, 50% medium, 20% long
        short_count = max(1, int(num_days * 0.3))
        long_count = max(1, int(num_days * 0.2))
        medium_count = num_days - short_count - long_count

        length_distribution = (["short"] * short_count +
                             ["medium"] * medium_count +
                             ["long"] * long_count)

        # Shuffle to avoid clustering
        random.shuffle(length_distribution)
    else:
        # Long-term pattern: maintain balance with variety
        # Create repeating pattern with some randomization
        pattern_length = 10
        base_pattern = ["short", "medium", "long", "medium", "short",
                       "medium", "long", "medium", "short", "medium"]

        full_cycles = num_days // pattern_length
        remainder = num_days % pattern_length

        length_distribution = base_pattern * full_cycles + base_pattern[:remainder]

        # Add some controlled randomization to avoid predictability
        # Swap some adjacent elements
        for i in range(0, len(length_distribution) - 1, 5):
            if i + 1 < len(length_distribution) and random.random() < 0.3:
                length_distribution[i], length_distribution[i + 1] = length_distribution[i + 1], length_distribution[i]

    return length_distribution


def optimize_length_for_category(base_length: str, category: str) -> str:
    """
    Optimize post length based on content category.

    Args:
        base_length: Base length from distribution
        category: Content category

    Returns:
        Optimized length
    """
    # Category-specific length preferences
    category_preferences = {
        "tips": "medium",           # Tips work well in medium format
        "insights": "long",         # Insights need space to develop
        "questions": "short",       # Questions should be concise
        "stories": "long",          # Stories need narrative space
        "news": "medium",           # News commentary is typically medium
        "advice": "medium",         # Advice posts are usually medium
        "personal": "long",         # Personal posts often tell stories
        "industry": "medium",       # Industry posts are typically medium
        "leadership": "medium",     # Leadership content is usually medium
        "technology": "medium",     # Tech posts are often medium
        "career": "medium",         # Career advice is typically medium
        "education": "long",        # Educational content needs space
        "motivation": "short",      # Motivational posts are often punchy
        "announcement": "short"     # Announcements should be concise
    }

    # Get category preference
    category_lower = category.lower()
    preferred_length = None

    for cat_key, pref_length in category_preferences.items():
        if cat_key in category_lower:
            preferred_length = pref_length
            break

    # If no specific preference, use base length
    if not preferred_length:
        return base_length

    # Apply preference with some flexibility
    # If base and preferred are the same, use it
    if base_length == preferred_length:
        return base_length

    # If they differ, use preferred 70% of the time, base 30% of the time
    if random.random() < 0.7:
        return preferred_length
    else:
        return base_length


def clean_html_for_query(html_content: str) -> str:
    """Remove HTML tags and extra whitespace to create a clean query string."""
    clean_text = re.sub(r'<[^>]+>', '', html_content)
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
    return clean_text

def should_post_have_url(post_content: str) -> bool:
    """Use LLM to determine if a post should include a URL."""
    prompt = (
        "Should this LinkedIn post include a relevant external URL? Respond only with true or false.\n"
        f"Post:\n{post_content}"
    )
    try:
        response = model.generate_content(prompt)
        answer = response.text.strip().lower()
        return answer.startswith('true')
    except Exception as e:
        logging.error(f"Error in should_post_have_url: {e}")
        return False

# --- NEW: Refactored helper function for calculating category distribution ---
def _calculate_category_distribution(num_days: int, categories: List[str]) -> List[str]:
    """Calculates the distribution of categories over the number of days."""
    if not categories:
        return ["General"] * num_days
        
    if num_days <= len(categories):
        distribution = categories[:num_days]
    else:
        base_count = num_days // len(categories)
        remainder = num_days % len(categories)
        distribution = categories * base_count
        if remainder > 0:
            distribution.extend(random.sample(categories, remainder))
    
    random.shuffle(distribution)
    return distribution

# --- IMPROVED: Refactored post generation with intelligent framework selection and natural persona integration ---
def _generate_and_process_post_for_day(day_index: int, start_dt: datetime, category: str, persona_data: Dict, used_opening_words: set, opening_words_lock: threading.Lock, url_budget: threading.Semaphore, assigned_length: str = "medium") -> Dict:
    """
    Generates a single post using intelligent framework selection and natural persona integration.
    This function is designed to be run in a separate thread.
    """
    post_date = start_dt + timedelta(days=day_index)
    post_datetime_str = (post_date.replace(
        hour=random.randint(8, 18),
        minute=random.choice([0, 15, 30, 45]))
    ).strftime("%Y-%m-%d %H:%M")

    # Optimize length for category
    optimized_length = optimize_length_for_category(assigned_length, category)

    # --- NEW: Use intelligent category-based prompt creation ---
    custom_prompt = create_intelligent_category_prompt(category, persona_data, optimized_length, day_index)

    # Get framework and style information for logging
    framework_mapping = get_category_framework_mapping(category)
    # --- MODIFICATION: Access the name from the details object ---
    selected_framework = framework_mapping["framework_details"]["name"]
    selected_style = framework_mapping["post_style"]
    persona_relevance = framework_mapping["persona_relevance"]

    logging.info(f"Day {day_index}: Using {selected_framework} framework and '{selected_style}' style for {category} (persona: {persona_relevance})")

    with opening_words_lock:
        current_used_words = used_opening_words.copy()

    # Pass the selected style to the post generator
    raw_post_response = generate_post_from_persona_keywords(
        persona_data.get('general_persona_keywords', []),
        None,  # Tone is still handled intelligently by the LLM
        selected_style,
        custom_prompt,
        content_interests=persona_data.get('content_persona_keywords'),
        network_interests=persona_data.get('network_persona_keywords'),
        add_emojis=persona_data.get('add_emojis', True),
        add_hashtags=persona_data.get('add_hashtags', True),
        use_hook_generator=True,
        used_opening_words=current_used_words,
        length=None  # Use default length for scheduled posts
    )

    # --- IMPROVED: Use centralized parser ---
    parsed_post = _parse_post_generation_response(raw_post_response)
    if not parsed_post:
        logging.warning(f"Failed to generate or parse post for day {day_index}.")
        # Return a minimal structure to avoid breaking the schedule
        return {
            "post_id": str(uuid.uuid4()),
            "scheduled_datetime": post_datetime_str,
            "content": "Content generation failed for this slot. Please regenerate.",
            "category": category,
            "framework": selected_framework,
            "persona_integration": persona_relevance,
            "day_index": day_index,
            "has_image": False, "has_infographics": False, "url": None
        }

    post_content = parsed_post["content"]

    # Track unique opening words
    if post_content:
        clean_text = clean_html_for_query(post_content)
        first_word = clean_text.split()[0].lower() if clean_text else ""
        if first_word:
            with opening_words_lock:
                if first_word not in used_opening_words:
                    used_opening_words.add(first_word)

    # Media analysis and URL fetching
    media_analysis = analyze_post_for_media(post_content)
    post_url = None

    # --- IMPROVED: Integrated URL logic within the thread ---
    if url_budget.acquire(blocking=False): # Non-blocking attempt to acquire semaphore
        if should_post_have_url(post_content):
            try:
                import asyncio
                clean_text = clean_html_for_query(post_content)
                search_query = generate_search_query_from_content(clean_text)
                # Run the async function in the current thread's event loop
                post_url = asyncio.run(fetch_related_url(search_query))
            except Exception as e:
                logging.error(f"Error fetching URL for day {day_index}: {e}")
                url_budget.release() # Release budget if URL fetch fails

    # --- IMPROVED: Extract tone from the generated post response ---
    generated_tone = parsed_post.get("tone", "Professional")

    return {
        "post_id": str(uuid.uuid4()),
        "scheduled_datetime": post_datetime_str,
        "style": selected_style,
        "tone": generated_tone,
        "length": optimized_length.title(),
        "content": post_content,
        "category": category,
        "framework": selected_framework,
        "persona_integration": persona_relevance,
        "framework_reason": framework_mapping["reason"],
        "has_image": media_analysis.get("has_image", False),
        "has_infographics": media_analysis.get("has_infographics", False),
        "url": post_url,
        "day_index": day_index
    }


# --- REFACTORED: Main scheduling function is now cleaner and more efficient ---
def generate_post_schedule(persona_data, schedule_duration, start_date=None, end_date=None, template=None):
    """
    Generates a post schedule using parallel processing with integrated URL fetching.
    """
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        num_days = (end_dt - start_dt).days + 1
    else:
        start_dt = datetime.today()
        num_days = 7

    # Setup for parallel execution
    used_opening_words = set()
    opening_words_lock = threading.Lock()
    
    # --- IMPROVED: Use a semaphore to limit the number of URLs ---
    url_limit = 3 if num_days >= 7 else max(1, num_days // 2)
    url_budget = threading.Semaphore(url_limit)
    
    categories = persona_data.get('categories', [])
    category_distribution = _calculate_category_distribution(num_days, categories)

    # Create balanced length distribution for variety
    length_distribution = create_balanced_length_distribution(num_days, categories)
    logging.info(f"Created balanced length distribution: {dict(zip(range(num_days), length_distribution))}")

    posts_data = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, num_days)) as executor:
        future_to_day = {
            executor.submit(
                _generate_and_process_post_for_day,
                day,
                start_dt,
                category_distribution[day],
                persona_data,
                used_opening_words,
                opening_words_lock,
                url_budget,
                length_distribution[day]  # Pass the assigned length for this day
            ): day for day in range(num_days)
        }
        
        for future in concurrent.futures.as_completed(future_to_day):
            try:
                result = future.result()
                posts_data.append(result)
            except Exception as e:
                logging.error(f"A post generation task failed: {e}")

    # Final processing and cleanup
    posts_data.sort(key=lambda x: x["day_index"])
    for post in posts_data:
        del post["day_index"]

    return posts_data