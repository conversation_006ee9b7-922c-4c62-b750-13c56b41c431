"""
Chatbot State Management
Handles conversation state, memory, and user context for the agentic chatbot.
"""

from typing import Dict, List, Optional, Any, Annotated
from dataclasses import field
from datetime import datetime
import json
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.pydantic_v1 import BaseModel

class UserProfile(BaseModel):
    """User profile information extracted from conversations"""
    general_persona_keywords: List[str] = []
    content_persona_keywords: List[str] = []
    network_persona_keywords: List[str] = []
    preferred_tone: str = "Professional"
    preferred_style: str = "Informative" 
    preferred_length: str = "medium"
    industry: Optional[str] = None
    interests: List[str] = []
    last_updated: datetime = datetime.now()
    
    class Config:
        arbitrary_types_allowed = True

class ConversationState(BaseModel):
    """State management for chatbot conversations"""
    
    # Conversation tracking
    session_id: str
    messages: List[BaseMessage] = []
    current_intent: Optional[str] = None
    last_agent_used: Optional[str] = None
    conversation_started: datetime = datetime.now()
    last_activity: datetime = datetime.now()
    
    # User context
    user_profile: UserProfile = UserProfile()
    user_preferences: Dict[str, Any] = {}
    
    # Current task context
    current_task: Optional[Dict[str, Any]] = None
    pending_actions: List[Dict[str, Any]] = []
    generated_content: List[Dict[str, Any]] = []
    
    # Agent routing history
    agent_history: List[Dict[str, str]] = []
    
    class Config:
        arbitrary_types_allowed = True
    
    def add_message(self, message: BaseMessage):
        """Add a message to the conversation history"""
        self.messages.append(message)
        self.last_activity = datetime.now()
    
    def add_human_message(self, content: str):
        """Add a human message to the conversation"""
        self.add_message(HumanMessage(content=content))
    
    def add_ai_message(self, content: str):
        """Add an AI message to the conversation"""
        self.add_message(AIMessage(content=content))
    
    def set_current_intent(self, intent: str, agent: str):
        """Set the current intent and agent"""
        self.current_intent = intent
        self.last_agent_used = agent
        self.agent_history.append({
            "timestamp": datetime.now().isoformat(),
            "intent": intent,
            "agent": agent
        })
    
    def update_user_profile(self, **kwargs):
        """Update user profile information"""
        for key, value in kwargs.items():
            if hasattr(self.user_profile, key):
                setattr(self.user_profile, key, value)
        self.user_profile.last_updated = datetime.now()
    
    def set_current_task(self, task_type: str, details: Dict[str, Any]):
        """Set the current task being worked on"""
        self.current_task = {
            "type": task_type,
            "details": details,
            "started": datetime.now().isoformat()
        }
    
    def add_generated_content(self, content_type: str, content: Dict[str, Any]):
        """Add generated content to the session"""
        self.generated_content.append({
            "type": content_type,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
    
    def get_conversation_context(self, last_n_messages: int = 10) -> str:
        """Get recent conversation context as a string"""
        recent_messages = self.messages[-last_n_messages:] if len(self.messages) > last_n_messages else self.messages
        context = []
        
        for msg in recent_messages:
            role = "Human" if isinstance(msg, HumanMessage) else "AI"
            context.append(f"{role}: {msg.content}")
        
        return "\n".join(context)
    
    def get_user_context_summary(self) -> str:
        """Get a summary of user context for agent prompts"""
        profile = self.user_profile
        context_parts = []
        
        if profile.general_persona_keywords:
            context_parts.append(f"User persona: {', '.join(profile.general_persona_keywords)}")
        
        if profile.industry:
            context_parts.append(f"Industry: {profile.industry}")
        
        if profile.interests:
            context_parts.append(f"Interests: {', '.join(profile.interests)}")
        
        context_parts.append(f"Preferred tone: {profile.preferred_tone}")
        context_parts.append(f"Preferred style: {profile.preferred_style}")
        
        if self.current_task:
            context_parts.append(f"Current task: {self.current_task['type']}")
        
        return " | ".join(context_parts)


# Global state manager
class StateManager:
    """Manages conversation states across sessions"""
    
    def __init__(self):
        self._states: Dict[str, ConversationState] = {}
    
    def get_state(self, session_id: str) -> ConversationState:
        """Get or create conversation state for a session"""
        if session_id not in self._states:
            self._states[session_id] = ConversationState(session_id=session_id)
        return self._states[session_id]
    
    def update_state(self, session_id: str, state: ConversationState):
        """Update conversation state"""
        self._states[session_id] = state
    
    def clear_state(self, session_id: str):
        """Clear conversation state for a session"""
        if session_id in self._states:
            del self._states[session_id]
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs"""
        return list(self._states.keys())


# Global state manager instance
state_manager = StateManager()
