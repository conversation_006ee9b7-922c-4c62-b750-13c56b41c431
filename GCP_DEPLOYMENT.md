# Google Cloud Platform Deployment Guide

This guide explains how to deploy the LinkedIn Content Creation API to Google Cloud Platform (GCP) using Cloud Run.

## Prerequisites

1. A Google Cloud Platform account
2. Google Cloud SDK installed locally
3. <PERSON><PERSON> installed locally
4. Access to Google Cloud Container Registry and Cloud Run

## Setup Google Cloud Project

1. Create a new project or select an existing one:
   ```bash
   gcloud projects create [PROJECT_ID] --name="LinkedIn Content API"
   # or
   gcloud config set project [PROJECT_ID]
   ```

2. Enable required APIs:
   ```bash
   gcloud services enable cloudbuild.googleapis.com
   gcloud services enable run.googleapis.com
   gcloud services enable artifactregistry.googleapis.com
   gcloud services enable aiplatform.googleapis.com
   ```

## Authentication Setup

### Create a Service Account for Cloud Run (Required)

1. Create a service account:
   ```bash
   gcloud iam service-accounts create linkedin-api-sa \
     --display-name="LinkedIn API Service Account"
   ```

2. Grant necessary permissions:
   ```bash
   # Grant Vertex AI User role
   gcloud projects add-iam-policy-binding [PROJECT_ID] \
     --member="serviceAccount:linkedin-api-sa@[PROJECT_ID].iam.gserviceaccount.com" \
     --role="roles/aiplatform.user"

   # Grant Vertex AI Model User role
   gcloud projects add-iam-policy-binding [PROJECT_ID] \
     --member="serviceAccount:linkedin-api-sa@[PROJECT_ID].iam.gserviceaccount.com" \
     --role="roles/aiplatform.modelUser"
   ```

3. For local testing, create and download a key:
   ```bash
   # Create a directory for the key if it doesn't exist
   mkdir -p app/utils/google

   # Create and download the key
   gcloud iam service-accounts keys create app/utils/google/service-account-key.json \
     --iam-account=linkedin-api-sa@[PROJECT_ID].iam.gserviceaccount.com
   ```

### Important Note for Cloud Run Deployment

The application is configured to automatically detect when it's running in Cloud Run and will use the service account assigned to the Cloud Run service. The cloudbuild.yaml file is configured to use the linkedin-api-sa service account for the Cloud Run service.

## Deployment Options

### Option 1: Manual Deployment

1. Build the Docker image:
   ```bash
   docker build -t gcr.io/[PROJECT_ID]/linkedin-api .
   ```

2. Push the image to Container Registry:
   ```bash
   docker push gcr.io/[PROJECT_ID]/linkedin-api
   ```

3. Deploy to Cloud Run:
   ```bash
   gcloud run deploy linkedin-api \
     --image gcr.io/[PROJECT_ID]/linkedin-api \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --memory 512Mi \
     --cpu 1 \
     --min-instances 0 \
     --max-instances 10 \
     --set-env-vars PROJECT_ID=[PROJECT_ID],LOCATION=us-central1,MODEL_NAME=gemini-2.5-flash
   ```

### Option 2: Automated Deployment with Cloud Build

1. Submit the build to Cloud Build:
   ```bash
   gcloud builds submit --config cloudbuild.yaml
   ```

This will automatically build the Docker image, push it to Container Registry, and deploy it to Cloud Run.

## Environment Variables

The following environment variables are set during deployment:

- `PROJECT_ID`: Your Google Cloud project ID
- `LOCATION`: The region where your Vertex AI resources are located (e.g., us-central1)
- `MODEL_NAME`: The Vertex AI model to use (e.g., gemini-2.5-flash)

## Monitoring and Logging

1. View Cloud Run service logs:
   ```bash
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=linkedin-api" --limit 10
   ```

2. Monitor service performance:
   ```bash
   gcloud run services describe linkedin-api
   ```

## Updating the Deployment

To update your deployment after making changes:

1. Rebuild the Docker image:
   ```bash
   docker build -t gcr.io/[PROJECT_ID]/linkedin-api .
   ```

2. Push the updated image:
   ```bash
   docker push gcr.io/[PROJECT_ID]/linkedin-api
   ```

3. Update the Cloud Run service:
   ```bash
   gcloud run services update linkedin-api --image gcr.io/[PROJECT_ID]/linkedin-api
   ```

Or simply run the Cloud Build process again:
```bash
gcloud builds submit --config cloudbuild.yaml
```

## Troubleshooting

1. Check service status:
   ```bash
   gcloud run services describe linkedin-api
   ```

2. View detailed logs:
   ```bash
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=linkedin-api" --limit 50
   ```

3. Test the health endpoint:
   ```bash
   curl https://[YOUR-SERVICE-URL]/health
   ```

4. If you encounter authentication issues, verify that the service account has the necessary permissions.
