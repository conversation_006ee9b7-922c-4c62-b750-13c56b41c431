
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Project settings
PROJECT_ID = "ai-linkedin-457504"
LOCATION = "us-central1"

# Vertex AI model settings
# Use env override if present; default to gemini-2.0-flash for consistency
MODEL_NAME = os.environ.get("MODEL_NAME", "gemini-2.0-flash")

# Check if we're running in GCP environment (Cloud Run)
gcp_env = os.environ.get('K_SERVICE') is not None

# In GCP, we don't need a service account key file
if gcp_env:
    logger.info("Running in GCP environment, no service account key needed")
    SERVICE_ACCOUNT_KEY = None
else:
    # Path to service account key file for local development
    SERVICE_ACCOUNT_KEY = os.path.join(os.path.dirname(os.path.abspath(__file__)), "app/utils/google/ai-linkedin-457504-9a444188a552.json")

    # Log the path we're trying to use
    logger.info(f"Looking for service account key at: {SERVICE_ACCOUNT_KEY}")

    # If the file doesn't exist at that path, try alternative paths
    if not os.path.exists(SERVICE_ACCOUNT_KEY):
        # Try alternative paths
        alt_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "utils/google/ai-linkedin-457504-9a444188a552.json"),
            "/app/app/utils/google/ai-linkedin-457504-9a444188a552.json",
            "/app/utils/google/ai-linkedin-457504-9a444188a552.json",
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "app/ai-linkedin-457504-9a444188a552.json")
        ]

        for path in alt_paths:
            logger.info(f"Trying alternative path: {path}")
            if os.path.exists(path):
                SERVICE_ACCOUNT_KEY = path
                logger.info(f"Found service account key at: {SERVICE_ACCOUNT_KEY}")
                break
        else:
            logger.warning("Service account key file not found in any of the expected locations")
            # Set to None to indicate it wasn't found
            SERVICE_ACCOUNT_KEY = None