# --- START OF FILE hashtag_generator.py ---

import re
import hashlib
from functools import lru_cache
from typing import List, Dict

from app.utils.model_initializer import model

# A private cache to store generated hashtags and avoid redundant API calls.
_hashtag_cache = {}

@lru_cache(maxsize=1000)
def _get_cache_key(post_content: str) -> str:
    """
    Generate a unique cache key for a given post content.
    
    Args:
        post_content: The content of the LinkedIn post.
        
    Returns:
        A unique MD5 hash string representing the content.
    """
    return hashlib.md5(post_content.encode()).hexdigest()

def _extract_hashtags_from_text(text: str) -> List[str]:
    """
    Extracts and cleans hashtags from a raw string using regex.
    
    Args:
        text: The string containing potential hashtags.
        
    Returns:
        A list of cleaned hashtags (e.g., ["#Marketing", "#Tech"]).
    """
    # Regex to find words starting with #
    hashtag_pattern = r'#\w+'
    hashtags = re.findall(hashtag_pattern, text)
    
    cleaned_hashtags = []
    for hashtag in hashtags:
        # Remove any trailing punctuation that might be attached
        clean_hashtag = hashtag.rstrip('.,!?;:')
        # Ensure it's a valid hashtag
        if len(clean_hashtag) > 1:
            cleaned_hashtags.append(clean_hashtag)
            
    return cleaned_hashtags

def generate_hashtags(post_content: str, interests: List[str] = None, disable_cache: bool = False, add_extra_hashtags: bool = False) -> str:
    """
    Generate a string of relevant and professional hashtags for a LinkedIn post.

    This is the primary function for hashtag generation. It analyzes the post content
    and optional user interests to create context-aware hashtags.

    Args:
        post_content: The full content of the LinkedIn post.
        interests: An optional list of user interests to guide the generation.
        disable_cache: If True, bypass the cache and generate fresh hashtags.
        add_extra_hashtags: If True, generates 2 additional hashtags for variety.

    Returns:
        A single string containing space-separated hashtags (e.g., "#Tech #AI #Innovation").
    """
    if not post_content and not interests:
        return ""

    cache_key = _get_cache_key(post_content)
    
    if not disable_cache and cache_key in _hashtag_cache:
        cached_result = _hashtag_cache[cache_key]
        if add_extra_hashtags:
            # Generate 2 additional hashtags that are different from the cached ones
            additional_hashtags = _generate_additional_hashtags(post_content, interests, cached_result)
            return f"{cached_result} {additional_hashtags}".strip()
        return cached_result

    # Determine the number of hashtags to request
    num_hashtags = 7 if add_extra_hashtags else 5
    
    # Create a detailed, high-quality prompt for the AI model
    prompt_context = f"Post Content:\n\"\"\"\n{post_content}\n\"\"\""
    if interests:
        prompt_context += f"\n\nUser Interests: {', '.join(interests)}"

    prompt = f"""
    You are an expert LinkedIn content strategist. Your task is to generate {num_hashtags} highly relevant, professional, and engaging hashtags based on the provided post content and user interests.

    {prompt_context}

    **Analysis and Generation Requirements:**
    1.  **Analyze Context:** Deeply analyze the post's core message, tone, and keywords.
    2.  **Relevance is Key:** Generate hashtags that are directly related to the main topics of the post.
    3.  **Professionalism:** All hashtags must be appropriate for a professional platform like LinkedIn.
    4.  **Avoid Generic Tags:** Do NOT use overly broad or unhelpful hashtags such as #business, #success, #motivation, #linkedin, #career, #innovation, #management, #professional, #update.
    5.  **No Spammy Tags:** Avoid hashtags like #trending, #viral, #like, #follow, or #instagood.
    6.  **Formatting:** Return ONLY the generated hashtags, separated by spaces. Do not include any explanations, titles, or other text.

    **Example Output:**
    #ContentStrategy #DigitalMarketing #SEO #BrandVoice #ThoughtLeadership
    """

    try:
        response = model.generate_content(prompt)
        hashtags_text = response.text.strip()
        
        # Extract hashtags from the model's response
        hashtags = _extract_hashtags_from_text(hashtags_text)
        
        # Deduplicate while preserving order
        unique_hashtags = list(dict.fromkeys(hashtags))

        if not unique_hashtags:
            # Fallback in case the AI fails to generate valid hashtags
            raise ValueError("AI returned no valid hashtags.")

        result = ' '.join(unique_hashtags[:num_hashtags])

    except Exception as e:
        print(f"Error generating hashtags, using fallback. Error: {str(e)}")
        # Provide a safe, generic fallback if AI generation fails
        fallback_hashtags = ["#ProfessionalDevelopment", "#Networking", "#Leadership", "#PersonalGrowth", "#FutureOfWork"]
        result = ' '.join(fallback_hashtags)

    if not disable_cache:
        _hashtag_cache[cache_key] = result
        # Periodically clean the cache to manage memory
        if len(_hashtag_cache) > 2000:
            _cleanup_hashtag_cache()

    return result

def _generate_additional_hashtags(post_content: str, interests: List[str], existing_hashtags: str) -> str:
    """
    Generates 2 additional hashtags that are different from the existing ones.
    
    Args:
        post_content: The content of the post.
        interests: A list of user interests.
        existing_hashtags: A string of already generated hashtags.
        
    Returns:
        A string of 2 new space-separated hashtags.
    """
    prompt_context = f"Post Content:\n\"\"\"\n{post_content}\n\"\"\""
    if interests:
        prompt_context += f"\n\nUser Interests: {', '.join(interests)}"

    prompt = f"""
    You are a LinkedIn content strategist. Based on the following content, generate exactly 2 additional, unique, and professional hashtags that are NOT present in this existing list: {existing_hashtags}.

    {prompt_context}

    **Requirements:**
    - Generate exactly 2 hashtags.
    - They must be different from the existing ones.
    - They must be relevant, professional, and engaging.
    - Return ONLY the two hashtags, separated by a space.

    **Additional Hashtags:**
    """
    try:
        response = model.generate_content(prompt)
        hashtags_text = response.text.strip()
        new_hashtags = _extract_hashtags_from_text(hashtags_text)
        return ' '.join(list(dict.fromkeys(new_hashtags))[:2])
    except Exception as e:
        print(f"Error generating additional hashtags: {str(e)}")
        return "#Community #Engagement" # Safe fallback


def fetch_popular_hashtags(general_persona_keywords: List[str], 
                          content_persona_keywords: List[str] = None, 
                          network_persona_keywords: List[str] = None) -> Dict[str, List[str]]:
    """
    Fetch popular and relevant hashtags based on persona keywords.

    This function maintains the contract required by the `/popular-posts-hashtags` endpoint
    by using the improved, context-aware generation logic.

    Args:
        general_persona_keywords: Core keywords describing the user's persona.
        content_persona_keywords: Keywords related to content interests.
        network_persona_keywords: Keywords related to network interests.

    Returns:
        A dictionary with two lists: 'persona_hashtags' and 'universal_hashtags'.
    """
    # Combine all keywords to create a representative context for hashtag generation
    all_keywords = (general_persona_keywords or []) + \
                   (content_persona_keywords or []) + \
                   (network_persona_keywords or [])
    
    if not all_keywords:
        # Provide a default set if no keywords are available
        return {
            "persona_hashtags": ["#Leadership", "#CareerGrowth", "#Upskilling", "#Mentorship"],
            "universal_hashtags": ["#FutureOfWork", "#DigitalTransformation", "#WorkLifeBalance", "#TeamCulture"]
        }

    # Create a descriptive sentence to act as the "post content" for the generator
    representative_content = (
        f"A professional in the fields of {', '.join(general_persona_keywords)}. "
        f"Interested in content about {', '.join(content_persona_keywords)}. "
        f"Looking to network with experts in {', '.join(network_persona_keywords)}."
    )
    
    # Use the main generator to get 8 high-quality hashtags
    hashtag_string = generate_hashtags(representative_content, disable_cache=True, add_extra_hashtags=True)
    all_hashtags = hashtag_string.split()
    
    # Ensure we have 8 hashtags, adding from a fallback if necessary
    fallback_universal = ["#Storytelling", "#BrandVoice", "#AudienceEngagement", "#ContentStrategy", "#ThoughtLeadership", "#Mentorship", "#Networking", "#GrowthMindset"]
    for tag in fallback_universal:
        if len(all_hashtags) < 8 and tag not in all_hashtags:
            all_hashtags.append(tag)
            
    # Split the results into the two required categories
    return {
        "persona_hashtags": all_hashtags[:4],
        "universal_hashtags": all_hashtags[4:8]
    }

def _cleanup_hashtag_cache():
    """
    Cleans the hashtag cache by keeping only the most recent 1000 entries
    to prevent excessive memory usage.
    """
    global _hashtag_cache
    if len(_hashtag_cache) > 1000:
        # Convert cache to a list of items and keep the last 1000
        items = list(_hashtag_cache.items())
        _hashtag_cache = dict(items[-1000:])

def clear_hashtag_cache():
    """Explicitly clears the entire hashtag cache."""
    global _hashtag_cache
    _hashtag_cache.clear()

# --- END OF FILE hashtag_generator.py ---