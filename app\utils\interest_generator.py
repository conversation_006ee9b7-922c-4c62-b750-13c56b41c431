# interest_generator.py
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES

def generate_interests(user_attributes):
    """Generate 8-10 professional interests based on user profile.

    Args:
        user_attributes: Dictionary containing user profile information

    Returns:
        List of interests relevant to the user's profile
    """
    # Build user profile
    work_experience_str = ""
    if user_attributes.get("work_experience"):
        work_experience_str = "\nWork Experience:\n"
        for exp in user_attributes["work_experience"]:
            work_experience_str += (
                f"- {exp.get('position', 'Position')}, {exp.get('company', 'Company')}, "
                f"{exp.get('location', 'Location')}, {exp.get('startDate', 'Start Date')} - {exp.get('endDate', 'End Date')}, "
                f"Industry: {exp.get('industry', 'Industry')}, Description: {exp.get('job_description', 'No Description')}\n"
            )

    education_str = ""
    if user_attributes.get("education"):
        education_str = "\nEducation:\n"
        for edu in user_attributes["education"]:
            education_str += f"- {edu.get('degree', 'Degree')}, {edu.get('school_name', 'School')}, {edu.get('years_of_study', 'Years')}\n"

    user_profile = (
        f"Name: {user_attributes.get('name', 'N/A')}\n"
        f"Headline: {user_attributes.get('headline', 'N/A')}\n"
        f"Summary: {user_attributes.get('summary', 'N/A')}\n"
        f"Current Role: {user_attributes.get('current_role', 'N/A')}\n"
        f"Organizations: {user_attributes.get('organizations', 'N/A')}\n"
        f"Industry: {user_attributes.get('industry', 'N/A')}\n"
        f"Location: {user_attributes.get('city', 'N/A')}, {user_attributes.get('country', 'N/A')}\n"
        f"Skills: {', '.join(user_attributes.get('skills', ['N/A']))}\n"
        f"Certifications: {', '.join(user_attributes.get('certifications', ['N/A']))}\n"
        f"Languages: {', '.join(user_attributes.get('languages', ['N/A']))}\n"
        f"{work_experience_str}"
        f"{education_str}"
    )

    # Generate interests using the template
    prompt = TEMPLATES["generate_interests"].format(user_profile=user_profile)
    response = model.generate_content(prompt)

    # Process the response to extract interests
    interests_text = response.text.strip()

    # Split by newlines and clean up
    interests = []
    for line in interests_text.split('\n'):
        # Remove numbering and whitespace
        cleaned_line = line.strip()
        # Check if line starts with a number followed by period or parenthesis
        if cleaned_line and (cleaned_line[0].isdigit() or cleaned_line[0] == '#' or cleaned_line[0] == '-'):
            # Remove the numbering/bullet and trim
            interest = cleaned_line.split('.', 1)[-1].split(')', 1)[-1].strip()
            if interest.startswith('-'):
                interest = interest[1:].strip()
            interests.append(interest)
        elif cleaned_line and len(cleaned_line) > 2:  # If it's not empty and not just a number
            interests.append(cleaned_line)

    return interests
