import os
import requests # Still used for synchronous model calls, can be kept for simplicity
import asyncio
import random
import aiohttp
import trafilatura
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from app.utils.model_initializer import model
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- MODIFICATION: Global ThreadPoolExecutor ---
# A single executor for all blocking tasks ensures efficient thread management.
executor = ThreadPoolExecutor(max_workers=15)


def extract_source_from_url(url: str) -> str:
    """
    Extract the source name from a URL.
    
    Args:
        url: The URL to extract source from
        
    Returns:
        The source name (e.g., "TechCrunch", "Reuters", "CNN")
    """
    try:
        if not url:
            return "Unknown Source"
        
        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove common prefixes
        domain = domain.replace('www.', '')
        
        # Common news source mappings
        source_mappings = {
            'techcrunch.com': 'TechCrunch',
            'reuters.com': 'Reuters',
            'cnn.com': 'CNN',
            'bbc.com': 'BBC',
            'bbc.co.uk': 'BBC',
            'nytimes.com': 'The New York Times',
            'wsj.com': 'The Wall Street Journal',
            'washingtonpost.com': 'The Washington Post',
            'forbes.com': 'Forbes',
            'bloomberg.com': 'Bloomberg',
            'cnbc.com': 'CNBC',
            'theverge.com': 'The Verge',
            'arstechnica.com': 'Ars Technica',
            'wired.com': 'Wired',
            'engadget.com': 'Engadget',
            'mashable.com': 'Mashable',
            'venturebeat.com': 'VentureBeat',
            'zdnet.com': 'ZDNet',
            'cnet.com': 'CNET',
            'techradar.com': 'TechRadar',
            'theguardian.com': 'The Guardian',
            'independent.co.uk': 'The Independent',
            'ft.com': 'Financial Times',
            'economist.com': 'The Economist',
            'hbr.org': 'Harvard Business Review',
            'linkedin.com': 'LinkedIn',
            'medium.com': 'Medium',
            'substack.com': 'Substack',
            'github.com': 'GitHub',
            'stackoverflow.com': 'Stack Overflow',
            'reddit.com': 'Reddit',
            'twitter.com': 'Twitter',
            'x.com': 'X (Twitter)',
            'youtube.com': 'YouTube',
        }
        
        # Check if we have a direct mapping
        if domain in source_mappings:
            return source_mappings[domain]
        
        # Extract from domain name (capitalize and clean)
        if '.' in domain:
            # Get the main part of the domain (before the first dot)
            main_part = domain.split('.')[0]
            # Capitalize and clean
            source = main_part.replace('-', ' ').replace('_', ' ').title()
            return source
        
        return "Unknown Source"
        
    except Exception as e:
        print(f"Error extracting source from URL {url}: {str(e)}")
        return "Unknown Source"


async def summarize_article(session: aiohttp.ClientSession, url: str, title: str = '', snippet: str = '') -> str:
    """
    Fetch the article content from the URL and generate a 2-3 line summary.
    If content is insufficient, use title and snippet. If those are missing, use title alone.
    Always generate a meaningful summary from available information.
    """
    summary_styles = [
        "Write a simple 2-3 line summary of this article for working professionals. Use easy words and focus on the main points and what they mean.",
        "Create a short 2-3 line summary of this article for business professionals. Use clear, simple language and highlight the main news.",
        "Give a brief 2-3 line overview of this article for LinkedIn professionals. Use everyday words and focus on practical takeaways.",
        "Write a 2-3 line summary of this article for professionals. Use simple words and focus on how this affects the industry.",
        "Summarize this article in 2-3 lines for working professionals. Use clear language and highlight trends and opportunities."
    ]
    
    prompt_template = random.choice(summary_styles)
    text_content = None
    loop = asyncio.get_running_loop()

    try:
        async with session.get(url, timeout=10) as response:
            response.raise_for_status()
            html_content = await response.text()
            # Offload CPU-bound parsing to executor
            text_content = await loop.run_in_executor(
                executor, trafilatura.extract, html_content
            )

        if text_content and len(text_content) >= 100:
            prompt = f"{prompt_template}\n\nArticle:\n{text_content[:4000]}"
        elif title or snippet:
            prompt = f"{prompt_template}\n\nTitle: {title}\nSnippet: {snippet}"
        elif title:
            prompt = f"{prompt_template}\n\nTitle: {title}"
        else:
            return "Summary not available for this article."

        # Offload blocking model call to executor
        blocking_call = partial(model.generate_content, prompt)
        response = await loop.run_in_executor(executor, blocking_call)
        summary = response.text.strip()
        
        summary_lines = [line for line in summary.split('\n') if line.strip()]
        return '\n'.join(summary_lines[:3])

    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
        logger.warning(f"Error fetching article {url} for summary: {str(e)}. Falling back.")
        try:
            if title or snippet:
                prompt = f"{prompt_template}\n\nTitle: {title}\nSnippet: {snippet}\nURL: {url}"
                # Offload blocking model call to executor
                blocking_call = partial(model.generate_content, prompt)
                response = await loop.run_in_executor(executor, blocking_call)
                summary = response.text.strip()
                summary_lines = [line for line in summary.split('\n') if line.strip()]
                return '\n'.join(summary_lines[:3])
            else:
                return "Summary not available for this article."
        except Exception as gen_e:
            logger.error(f"Fallback summary generation failed for {url}: {str(gen_e)}")
            return "Summary not available for this article."
    except Exception as e:
        logger.error(f"An unexpected error occurred during summarization for {url}: {str(e)}")
        return "Summary not available for this article."


@dataclass
class NewsArticle:
    """Data class for news articles"""
    url: str
    title: str
    snippet: str
    source: str
    published_at: Optional[str] = None
    summary: Optional[str] = None
    is_relevant: bool = True


@dataclass
class Category:
    """Data class for news categories"""
    name: str
    keywords: List[str]
    articles: List[NewsArticle] = field(default_factory=list)


class CategoryGeneratorAgent:
    """Agent responsible for generating up to 5 categories based on persona keywords"""
    
    def __init__(self):
        self.model = model
    
    async def generate_categories(self, 
                                general_persona_keywords: List[str],
                                content_persona_keywords: Optional[List[str]] = None,
                                network_persona_keywords: Optional[List[str]] = None) -> List[Category]:
        """
        Generate up to 5 unique categories, each composed of 2-3 words.
        Categories should be interesting, attention-grabbing, well-composed, and unique.
        """
        try:
            all_keywords = general_persona_keywords.copy()
            if content_persona_keywords:
                all_keywords.extend(content_persona_keywords)
            if network_persona_keywords:
                all_keywords.extend(network_persona_keywords)
            
            persona_description = f"Professional with keywords: {', '.join(all_keywords)}"
            
            prompt = f"""
You are a professional news category generator. Based on the following persona, generate exactly 5 unique news categories:
Persona: {persona_description}
Requirements:
1. Generate exactly 5 categories, each made of 2-3 words
2. Categories should be interesting, eye-catching, well-written, and unique
3. Each category should fit the person's work context and interests
4. Categories should be specific enough to find relevant news but broad enough to get results
5. Focus on work-related, industry-relevant categories
6. Avoid basic categories like "Technology News" or "Business Updates"
7. Make categories sound like professional LinkedIn content categories
8. Use simple, clear words that all professionals can understand
Format your response as a numbered list of exactly 5 categories, one per line.
"""
            
            # Offload blocking model call to executor
            loop = asyncio.get_running_loop()
            blocking_call = partial(self.model.generate_content, prompt)
            response = await loop.run_in_executor(executor, blocking_call)
            categories_text = response.text.strip()
            
            category_names = [line.split('.', 1)[1].strip() for line in categories_text.split('\n') if line and '.' in line]
            
            if len(category_names) != 5:
                raise ValueError("AI did not generate exactly 5 categories.")
            
            keyword_tasks = [self._generate_keywords_for_category(name, all_keywords) for name in category_names[:5]]
            all_keywords_for_categories = await asyncio.gather(*keyword_tasks)
            
            return [Category(name=name, keywords=keywords) for name, keywords in zip(category_names[:5], all_keywords_for_categories)]

        except Exception as e:
            logger.error(f"Error generating categories, falling back to defaults: {str(e)}")
            primary_keyword = general_persona_keywords[0] if general_persona_keywords else 'technology'
            fallback_names = [f"{primary_keyword} Innovation", f"{primary_keyword} Leadership", f"{primary_keyword} Trends", f"{primary_keyword} Development", f"{primary_keyword} Insights"]
            return [Category(name=name, keywords=[name.lower(), "news"]) for name in fallback_names]
    
    async def _generate_keywords_for_category(self, category_name: str, persona_keywords: List[str]) -> List[str]:
        """Generate relevant keywords for a specific category"""
        try:
            prompt = f"""
Generate 3-5 relevant keywords for the news category "{category_name}" based on these persona keywords: {', '.join(persona_keywords)}
Return only the keywords, separated by commas.
"""
            
            # Offload blocking model call to executor
            loop = asyncio.get_running_loop()
            blocking_call = partial(self.model.generate_content, prompt)
            response = await loop.run_in_executor(executor, blocking_call)
            keywords_text = response.text.strip()
            keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
            
            return keywords[:5] if len(keywords) >= 2 else [category_name.lower(), "latest", "news"]
            
        except Exception as e:
            logger.error(f"Error generating keywords for category {category_name}: {str(e)}")
            return [category_name.lower(), "latest", "news"]


class NewsRetrievalAgent:
    """Agent responsible for keyword extraction and news retrieval using Tavily API"""

    def __init__(self, tavily_key: str, session: aiohttp.ClientSession):
        self.tavily_key = tavily_key
        self.session = session
    
    async def fetch_news_for_category(self, category: Category) -> Tuple[List[NewsArticle], bool]:
        """
        Fetch up to 5 recent news articles for a category using Tavily API.
        Returns (articles, success_flag) where success_flag indicates if relevant news was found.
        """
        if not self.tavily_key:
            return [], False

        try:
            articles = await self._fetch_from_tavily(category, 5)

            validation_tasks = [self._validate_relevance(article, category) for article in articles]
            relevance_results = await asyncio.gather(*validation_tasks)

            relevant_articles = [article for article, is_relevant in zip(articles, relevance_results) if is_relevant]

            success = len(relevant_articles) > 0 # Success is finding at least one article
            return relevant_articles[:5], success

        except Exception as e:
            logger.error(f"Error fetching news for category {category.name}: {str(e)}")
            return [], False

    async def _fetch_from_tavily(self, category: Category, count: int) -> List[NewsArticle]:
        """Fetch news articles from Tavily API asynchronously with a single, optimized call."""
        try:
            from tavily import TavilyClient
            client = TavilyClient(api_key=self.tavily_key)

            # A single, more comprehensive query is more efficient than multiple queries.
            query_parts = [category.name] + category.keywords[:2]
            comprehensive_query = f"latest news and recent developments on {' '.join(query_parts)}"
            
            logger.info(f"Performing consolidated Tavily search for: {comprehensive_query}")

            loop = asyncio.get_running_loop()
            # Offload the blocking Tavily search to the executor
            blocking_search = partial(
                client.search,
                query=comprehensive_query,
                search_depth="basic",
                topic="news",  # CRITICAL: Required for published_date field
                days=7,  # Get news from last 7 days to ensure recent articles with dates
                max_results=10,  # Fetch more to have a better pool to filter from
                include_domains=["reuters.com", "cnn.com", "bbc.com", "techcrunch.com", "bloomberg.com", "forbes.com", "wsj.com", "nytimes.com"]
            )
            response = await loop.run_in_executor(executor, blocking_search)

            all_articles = []
            for result in response.get('results', []):
                article = NewsArticle(
                    url=result.get('url', ''),
                    title=result.get('title', ''),
                    snippet=result.get('content', '')[:300] + '...' if result.get('content') else '',
                    source=extract_source_from_url(result.get('url', '')),
                    # --- DATE FIX: Use the correct key 'published_date' from Tavily API ---
                    published_at=result.get('published_date', None)
                )
                all_articles.append(article)

            # Remove duplicates - this remains a good practice
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url and article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)

            return unique_articles[:count]

        except Exception as e:
            logger.error(f"Error in _fetch_from_tavily: {str(e)}")
            return []

    async def _validate_relevance(self, article: NewsArticle, category: Category) -> bool:
        """Validate if an article is relevant to the category"""
        category_terms = [category.name.lower()] + [kw.lower() for kw in category.keywords]
        article_text = f"{article.title.lower()} {article.snippet.lower()}"
        
        score = sum(1 for term in category_terms if term in article_text)
        if category.name.lower() in article_text:
            score += 2 # Higher weight for exact category name match
        
        return score >= 2


class FallbackAgent:
    """Agent responsible for a single broader search when initial retrieval fails completely"""

    def __init__(self, tavily_key: str, session: aiohttp.ClientSession):
        self.tavily_key = tavily_key
        self.session = session
    
    async def fetch_fallback_news(self, category: Category) -> List[NewsArticle]:
        """Perform a single, broader search to find relevant news using Tavily API."""
        if not self.tavily_key:
            return []

        try:
            from tavily import TavilyClient
            client = TavilyClient(api_key=self.tavily_key)
            
            # Generate several potential queries but only use the best one.
            broader_queries = self._generate_broader_queries(category)
            if not broader_queries:
                return []

            fallback_query = broader_queries[0] # Use the first (best) one
            logger.info(f"Performing single fallback Tavily search for: {fallback_query}")

            loop = asyncio.get_running_loop()
            # Offload the blocking Tavily search to the executor
            blocking_search = partial(
                client.search,
                query=f"{fallback_query} within the last month",
                search_depth="basic",
                topic="news",  # CRITICAL: Required for published_date field
                days=30,  # Get news from last 30 days for fallback search
                max_results=5
            )
            response = await loop.run_in_executor(executor, blocking_search)

            all_articles = []
            for result in response.get('results', []):
                article = NewsArticle(
                    url=result.get('url', ''),
                    title=result.get('title', ''),
                    snippet=result.get('content', '')[:300] + '...' if result.get('content') else '',
                    source=extract_source_from_url(result.get('url', '')),
                    # --- DATE FIX: Use the correct key 'published_date' from Tavily API ---
                    published_at=result.get('published_date', None)
                )
                all_articles.append(article)
            
            # Remove duplicates
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url and article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)

            return unique_articles[:5]

        except Exception as e:
            logger.error(f"Error in fallback news retrieval for {category.name}: {str(e)}")
            return []

    def _generate_broader_queries(self, category: Category) -> List[str]:
        """Generate broader search queries for fallback"""
        base_terms = [category.name] + category.keywords[:2]
        industry_terms = ["industry", "sector", "trends", "developments"]
        time_terms = ["recent", "latest", "emerging"]
        
        broader_queries = [f"{base} {term}" for base in base_terms for term in industry_terms]
        broader_queries.extend([f"{term} {base}" for base in base_terms for term in time_terms])
        
        random.shuffle(broader_queries)
        return broader_queries[:3]


class SupervisorAgent:
    """Supervisor Agent that manages and coordinates all sub-agents"""

    def __init__(self, tavily_key: Optional[str]):
        self.tavily_key = tavily_key
        self.category_agent = CategoryGeneratorAgent()
        self.news_agent: Optional[NewsRetrievalAgent] = None
        self.fallback_agent: Optional[FallbackAgent] = None

    async def get_trending_news(self,
                               general_persona_keywords: List[str],
                               content_persona_keywords: Optional[List[str]] = None,
                               network_persona_keywords: Optional[List[str]] = None,
                               user_categories: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Main method that coordinates the entire agentic workflow.
        Returns the complete output formatted like the current /trending-news response.
        """
        async with aiohttp.ClientSession() as session:
            self.news_agent = NewsRetrievalAgent(self.tavily_key, session)
            self.fallback_agent = FallbackAgent(self.tavily_key, session)

            try:
                # Step 1: Generate or use categories
                if user_categories:
                    keyword_tasks = [self.category_agent._generate_keywords_for_category(name, general_persona_keywords) for name in user_categories[:5]]
                    keywords_list = await asyncio.gather(*keyword_tasks)
                    categories = [Category(name, keywords) for name, keywords in zip(user_categories[:5], keywords_list)]
                else:
                    categories = await self.category_agent.generate_categories(
                        general_persona_keywords, content_persona_keywords, network_persona_keywords)

                if not categories:
                    raise ValueError("Category generation failed.")

                # Step 2: Fetch news for all categories concurrently
                fetch_tasks = [self._fetch_and_fallback_for_category(category) for category in categories]
                category_article_results = await asyncio.gather(*fetch_tasks)

                # Step 3: Collect and deduplicate all articles before summarization
                unique_articles_map: Dict[str, NewsArticle] = {}
                for category, articles in zip(categories, category_article_results):
                    category.articles = articles
                    for article in articles:
                        if article.url not in unique_articles_map:
                            unique_articles_map[article.url] = article
                
                # Step 4: Summarize all unique articles concurrently
                summary_tasks = [summarize_article(session, article.url, article.title, article.snippet)
                                 for article in unique_articles_map.values()]
                summaries = await asyncio.gather(*summary_tasks)
                
                # Add summaries to the unique articles
                for article, summary in zip(unique_articles_map.values(), summaries):
                    article.summary = summary
                
                # Step 5: Build the final response
                final_results = []
                for category in categories:
                    summarized_articles_for_cat = [unique_articles_map[art.url] for art in category.articles if art.url in unique_articles_map]
                    
                    # We only need one good article to form a category now
                    if summarized_articles_for_cat:
                        final_results.append({
                            "category_name": category.name,
                            "articles": [{
                                "url": art.url, "title": art.title, "published_at": art.published_at,
                                "summary": art.summary, "source": art.source
                            } for art in summarized_articles_for_cat[:5]] # Limit to 5
                        })

                logger.info(f"Final result: {len(final_results)} categories with news")
                return {"news": final_results}

            except Exception as e:
                logger.error(f"Error in SupervisorAgent: {str(e)}")
                primary_keyword = general_persona_keywords[0] if general_persona_keywords else 'technology'
                return {
                    "news": [{
                        "category_name": f"{primary_keyword} News",
                        "articles": [{
                            "url": f"https://example.com/news/{primary_keyword.lower()}-{i+1}",
                            "title": f"Latest {primary_keyword} News #{i+1}",
                            "summary": f"Recent developments in {primary_keyword} industry",
                            "source": "Example News"
                        } for i in range(5)]
                    }]
                }

    async def _fetch_and_fallback_for_category(self, category: Category) -> List[NewsArticle]:
        """
        Helper to fetch news for a single category, with a more efficient fallback logic.
        Fallback is now only triggered on complete failure of the primary search.
        """
        articles, success = await self.news_agent.fetch_news_for_category(category)
        
        # Only use fallback if the primary, powerful search returned ZERO relevant articles.
        if not success:
            logger.warning(f"Initial retrieval failed for {category.name}, using single-shot fallback.")
            fallback_articles = await self.fallback_agent.fetch_fallback_news(category)
            return fallback_articles # No need to combine; the first set was empty.
        
        return articles


# Global supervisor instance initialization
TAVILY_API_KEY = os.environ.get("TAVILY_API_KEY")
if not TAVILY_API_KEY:
    print("Warning: TAVILY_API_KEY environment variable not set. News retrieval will be disabled.")
supervisor = SupervisorAgent(tavily_key=TAVILY_API_KEY)


async def get_trending_news_agentic(general_persona_keywords: List[str],
                                   content_persona_keywords: Optional[List[str]] = None,
                                   network_persona_keywords: Optional[List[str]] = None,
                                   categories: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Main function to get trending news using the agentic architecture.
    """
    return await supervisor.get_trending_news(
        general_persona_keywords=general_persona_keywords,
        content_persona_keywords=content_persona_keywords,
        network_persona_keywords=network_persona_keywords,
        user_categories=categories
    )