"""
LangChain Tools Integration
Wraps existing utility functions as LangChain tools for use by agents.
"""

from typing import List, Dict, Any, Optional
from langchain_core.tools import Tool
from langchain_core.pydantic_v1 import BaseModel, Field
import asyncio
import json

# Import existing utility functions
from app.utils.post_creator import generate_post_from_persona_keywords
from app.utils.content_modifier import modify_content
from app.utils.hashtag_generator import generate_hashtags
from app.utils.agentic_trending_news import get_trending_news_agentic
from app.utils.general_persona_generator import generate_general_persona
from app.utils.profile_scoring import score_profile
from app.utils.ai_content import generate_content_categories
from app.utils.post_management import generate_post_schedule
from app.utils.feed_categorizer import categorize_feed_posts

# Tool Input Models
class PostCreationInput(BaseModel):
    """Input for post creation tool"""
    persona_keywords: List[str] = Field(description="General persona keywords for the user")
    content_keywords: Optional[List[str]] = Field(default=None, description="Content-focused keywords")
    network_keywords: Optional[List[str]] = Field(default=None, description="Network-focused keywords")
    tone: str = Field(default="Professional", description="Tone of the post")
    style: str = Field(default="Informative", description="Style of the post")
    user_prompt: str = Field(description="User's request or topic for the post")
    length: str = Field(default="medium", description="Length of the post (short/medium/long)")

class ContentModificationInput(BaseModel):
    """Input for content modification tool"""
    original_content: str = Field(description="Original content to modify")
    tone: Optional[str] = Field(default=None, description="New tone for the content")
    style: Optional[str] = Field(default=None, description="New style for the content")
    length: Optional[str] = Field(default=None, description="New length for the content")
    add_emojis: bool = Field(default=False, description="Whether to add emojis")
    add_hashtags: bool = Field(default=False, description="Whether to add hashtags")
    keep_writing: bool = Field(default=False, description="Whether to extend content by adding 2 additional sentences")

class TrendingNewsInput(BaseModel):
    """Input for trending news tool"""
    persona_keywords: List[str] = Field(description="General persona keywords")
    content_keywords: Optional[List[str]] = Field(default=None, description="Content interests")
    network_keywords: Optional[List[str]] = Field(default=None, description="Network interests")
    categories: Optional[List[str]] = Field(default=None, description="Specific categories to search")

class PersonaGenerationInput(BaseModel):
    """Input for persona generation tool"""
    user_profile_data: Dict[str, Any] = Field(description="User profile information")

class ProfileScoringInput(BaseModel):
    """Input for profile scoring tool"""
    profile_data: Dict[str, Any] = Field(description="LinkedIn profile data to score")

class ContentCategoriesInput(BaseModel):
    """Input for content categories generation"""
    persona_keywords: List[str] = Field(description="General persona keywords")
    content_keywords: Optional[List[str]] = Field(default=None, description="Content keywords")
    network_keywords: Optional[List[str]] = Field(default=None, description="Network keywords")
    existing_categories: Optional[List[str]] = Field(default=None, description="Existing categories to expand")

class PostSchedulingInput(BaseModel):
    """Input for post scheduling tool"""
    persona_data: Dict[str, Any] = Field(description="Persona and preference data")
    start_date: str = Field(description="Start date for scheduling")
    end_date: str = Field(description="End date for scheduling")
    categories: List[str] = Field(description="Content categories")

# Tool Functions
def create_linkedin_post(
    persona_keywords: List[str],
    content_keywords: Optional[List[str]] = None,
    network_keywords: Optional[List[str]] = None,
    tone: str = "Professional",
    style: str = "Informative",
    user_prompt: str = ""
) -> str:
    """Create a LinkedIn post using the existing post creation system"""
    try:
        result = generate_post_from_persona_keywords(
            general_persona_keywords=persona_keywords,
            tone=tone,
            style=style,
            user_prompt=user_prompt,
            content_interests=content_keywords or [],
            network_interests=network_keywords or [],
            add_emojis=True,
            add_hashtags=True,
            use_hook_generator=True,
            length=None  # Use default length for chatbot posts
        )
        
        if isinstance(result, dict) and "posts" in result and len(result["posts"]) > 0:
            # Return the first post's content
            return result["posts"][0]["content"]
        elif isinstance(result, str):
            return result
        else:
            return "Failed to generate post content"
            
    except Exception as e:
        return f"Error creating post: {str(e)}"

def modify_existing_content(
    original_content: str,
    tone: Optional[str] = None,
    style: Optional[str] = None,
    length: Optional[str] = None,
    add_emojis: bool = False,
    add_hashtags: bool = False,
    keep_writing: bool = False
) -> str:
    """Modify existing content using the content modification system"""
    try:
        # Determine if length should be preserved (when no length parameter provided)
        preserve_length = length is None

        # Use the existing modify_content function with new parameter handling
        result = modify_content(
            original_content=original_content,
            tone=tone,
            style=style,
            length=length,
            keep_writing=keep_writing,
            preserve_length=preserve_length,
            disable_cache=True
        )

        # Handle emoji and hashtag additions if requested
        if add_emojis or add_hashtags:
            # Note: These features would need to be implemented separately
            # as they're not part of the core modify_content function
            pass

        return result
    except Exception as e:
        return f"Error modifying content: {str(e)}"

async def get_trending_news(
    persona_keywords: List[str],
    content_keywords: Optional[List[str]] = None,
    network_keywords: Optional[List[str]] = None,
    categories: Optional[List[str]] = None
) -> str:
    """Get trending news using the agentic news system"""
    try:
        result = await get_trending_news_agentic(
            general_persona_keywords=persona_keywords,
            content_persona_keywords=content_keywords,
            network_persona_keywords=network_keywords,
            categories=categories
        )
        
        # Format the result for the chatbot
        if "news" in result and result["news"]:
            formatted_news = []
            for category in result["news"]:
                category_info = f"**{category['category_name']}:**\n"
                for article in category["articles"][:3]:  # Show top 3 articles
                    category_info += f"• {article['title']} ({article['source']})\n"
                    category_info += f"  {article['summary']}\n\n"
                formatted_news.append(category_info)
            return "\n".join(formatted_news)
        else:
            return "No trending news found for your interests."
            
    except Exception as e:
        return f"Error fetching trending news: {str(e)}"

def generate_user_persona(user_profile_data: Dict[str, Any]) -> str:
    """Generate a user persona from profile data"""
    try:
        result = generate_general_persona(user_profile_data)
        
        # Format the persona information
        formatted_result = []
        if "general_persona_keywords" in result:
            formatted_result.append(f"**General Persona:** {', '.join(result['general_persona_keywords'])}")
        
        if "content_interests" in result:
            formatted_result.append(f"**Content Interests:** {', '.join(result['content_interests'])}")
        
        if "network_interests" in result:
            formatted_result.append(f"**Network Interests:** {', '.join(result['network_interests'])}")
        
        return "\n".join(formatted_result)
        
    except Exception as e:
        return f"Error generating persona: {str(e)}"

def score_linkedin_profile(profile_data: Dict[str, Any]) -> str:
    """Score a LinkedIn profile"""
    try:
        result = score_profile(profile_data)
        
        # Format the scoring result
        if "overall_score" in result:
            formatted_result = [f"**Overall Score:** {result['overall_score']}/100"]
            
            if "section_scores" in result:
                formatted_result.append("\n**Section Scores:**")
                for section, score in result["section_scores"].items():
                    formatted_result.append(f"• {section.title()}: {score}/100")
            
            if "suggestions" in result:
                formatted_result.append(f"\n**Suggestions:**\n{result['suggestions']}")
            
            return "\n".join(formatted_result)
        else:
            return "Profile scoring completed but no detailed results available."
            
    except Exception as e:
        return f"Error scoring profile: {str(e)}"

def generate_categories(
    persona_keywords: List[str],
    content_keywords: Optional[List[str]] = None,
    network_keywords: Optional[List[str]] = None,
    existing_categories: Optional[List[str]] = None
) -> str:
    """Generate content categories"""
    try:
        categories = generate_content_categories(
            general_persona_keywords=persona_keywords,
            content_persona_keywords=content_keywords,
            network_persona_keywords=network_keywords,
            categories=existing_categories
        )
        
        if categories:
            formatted_categories = []
            for category in categories:
                if isinstance(category, dict):
                    name = category.get("name", "")
                    description = category.get("description", "")
                    formatted_categories.append(f"• **{name}**: {description}")
                else:
                    formatted_categories.append(f"• {category}")
            
            return "\n".join(formatted_categories)
        else:
            return "No categories generated."
            
    except Exception as e:
        return f"Error generating categories: {str(e)}"

def schedule_posts(
    persona_data: Dict[str, Any],
    start_date: str,
    end_date: str,
    categories: List[str]
) -> str:
    """Generate a post schedule"""
    try:
        schedule = generate_post_schedule(
            persona_data=persona_data,
            schedule_duration="custom",
            start_date=start_date,
            end_date=end_date,
            template=None
        )
        
        if schedule:
            formatted_schedule = [f"**Post Schedule ({start_date} to {end_date}):**\n"]
            for i, post in enumerate(schedule[:5], 1):  # Show first 5 posts
                date = post.get("scheduled_datetime", "No date")
                category = post.get("category", "General")
                formatted_schedule.append(f"{i}. {date} - {category}")
            
            if len(schedule) > 5:
                formatted_schedule.append(f"... and {len(schedule) - 5} more posts")
            
            return "\n".join(formatted_schedule)
        else:
            return "No post schedule generated."
            
    except Exception as e:
        return f"Error generating schedule: {str(e)}"

# Create LangChain Tools
linkedin_tools = [
    Tool(
        name="create_linkedin_post",
        description="Create a LinkedIn post based on user persona and requirements. Use this when users want to generate new content.",
        func=lambda input_str: create_linkedin_post(**json.loads(input_str)),
        args_schema=PostCreationInput
    ),
    
    Tool(
        name="modify_content",
        description="Modify existing LinkedIn content by changing tone, style, length, or adding emojis/hashtags.",
        func=lambda input_str: modify_existing_content(**json.loads(input_str)),
        args_schema=ContentModificationInput
    ),
    
    Tool(
        name="get_trending_news",
        description="Fetch trending news relevant to the user's industry and interests. Use for news discovery and content inspiration.",
        func=lambda input_str: asyncio.run(get_trending_news(**json.loads(input_str))),
        args_schema=TrendingNewsInput
    ),
    
    Tool(
        name="generate_persona",
        description="Generate a professional persona from user profile data. Use when analyzing user's professional identity.",
        func=lambda input_str: generate_user_persona(**json.loads(input_str)),
        args_schema=PersonaGenerationInput
    ),
    
    Tool(
        name="score_profile",
        description="Score and analyze a LinkedIn profile for completeness and optimization opportunities.",
        func=lambda input_str: score_linkedin_profile(**json.loads(input_str)),
        args_schema=ProfileScoringInput
    ),
    
    Tool(
        name="generate_categories",
        description="Generate content categories for post planning and organization.",
        func=lambda input_str: generate_categories(**json.loads(input_str)),
        args_schema=ContentCategoriesInput
    ),
    
    Tool(
        name="schedule_posts",
        description="Create a posting schedule with dates and content categories.",
        func=lambda input_str: schedule_posts(**json.loads(input_str)),
        args_schema=PostSchedulingInput
    )
]

# Tool lookup dictionary for easy access
TOOL_MAP = {tool.name: tool for tool in linkedin_tools}
