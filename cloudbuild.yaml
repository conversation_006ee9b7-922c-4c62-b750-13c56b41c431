steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/linkedin-api', '.']

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/linkedin-api']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'linkedin-api'
      - '--image'
      - 'gcr.io/$PROJECT_ID/linkedin-api'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--min-instances'
      - '0'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'PROJECT_ID=$PROJECT_ID,LOCATION=us-central1,MODEL_NAME=gemini-2.0-flash,PYTHONPATH=/app,K_SERVICE=linkedin-api'
      - '--service-account'
      - 'linkedin-api-sa@$PROJECT_ID.iam.gserviceaccount.com'
      - '--description'
      - 'LinkedIn Content Creation API with Vertex AI integration'
      - '--timeout'
      - '300s'
      - '--concurrency'
      - '80'

images:
  - 'gcr.io/$PROJECT_ID/linkedin-api'

timeout: '1800s'

options:
  logging: CLOUD_LOGGING_ONLY
