# general_persona_generator.py
import json
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES

def generate_general_persona(request_data):
    """Generate a general persona based on user profile and optional content/network data.

    Args:
        request_data: Dictionary containing:
            - user_profile: Dictionary with user profile information
            - content: Dictionary with content data and interests
            - network: Dictionary with network data and interests

    Returns:
        Dictionary containing general persona keywords, content interests, network interests,
        content_persona_keywords, and network_persona_keywords
    """
    # Extract user profile data
    user_profile_data = request_data.get("user_profile", {})
    content_data = request_data.get("content", {})
    network_data = request_data.get("network", {})

    # Build user profile string
    work_experience_str = ""
    if user_profile_data.get("work_experience"):
        work_experience_str = "\nWork Experience:\n"
        for exp in user_profile_data["work_experience"]:
            work_experience_str += (
                f"- {exp.get('position', 'Position')}, {exp.get('company', 'Company')}, "
                f"{exp.get('location', 'Location')}, {exp.get('startDate', 'Start Date')} - {exp.get('endDate', 'End Date')}, "
                f"Industry: {exp.get('industry', 'Industry')}, Description: {exp.get('job_description', 'No Description')}\n"
            )

    education_str = ""
    if user_profile_data.get("education"):
        education_str = "\nEducation:\n"
        for edu in user_profile_data["education"]:
            if isinstance(edu, dict):
                # Only include fields that are not None
                edu_parts = []
                if edu.get('degree'):
                    edu_parts.append(edu['degree'])
                if edu.get('school_name'):
                    edu_parts.append(edu['school_name'])
                if edu.get('years_of_study'):
                    edu_parts.append(edu['years_of_study'])
                
                if edu_parts:
                    education_str += f"- {', '.join(edu_parts)}\n"
            else:
                # Handle legacy string format
                education_str += f"- {edu}\n"

    user_profile = (
        f"Name: {user_profile_data.get('name', 'N/A')}\n"
        f"Headline: {user_profile_data.get('headline', 'N/A')}\n"
        f"Summary: {user_profile_data.get('summary', 'N/A')}\n"
        f"Current Role: {user_profile_data.get('current_role', 'N/A')}\n"
        f"Organizations: {user_profile_data.get('organizations', 'N/A')}\n"
        f"Industry: {user_profile_data.get('industry', 'N/A')}\n"
        f"Location: {user_profile_data.get('city', 'N/A')}, {user_profile_data.get('country', 'N/A')}\n"
        f"Skills: {', '.join(user_profile_data.get('skills', ['N/A']))}\n"
        f"Certifications: {', '.join(user_profile_data.get('certifications', ['N/A']))}\n"
        f"Languages: {', '.join(user_profile_data.get('languages', ['N/A']))}\n"
        f"{work_experience_str}"
        f"{education_str}"
    )

    # Add content data information if available
    content_info = ""
    if content_data.get("data") and isinstance(content_data["data"], list) and content_data["data"]:
        content_info += "\nContent Data:\n"
        for item in content_data["data"]:
            if isinstance(item, dict):
                for key, value in item.items():
                    content_info += f"- {key}: {value}\n"
            else:
                content_info += f"- {item}\n"

    # Add content interests if available
    if content_data.get("content_interests") and content_data["content_interests"]:
        content_info += "\nProvided Content Interests:\n"
        content_info += f"- {', '.join(content_data['content_interests'])}\n"

    # Add network data information if available
    network_info = ""
    if network_data.get("data") and isinstance(network_data["data"], list) and network_data["data"]:
        network_info += "\nNetwork Data:\n"
        for item in network_data["data"]:
            if isinstance(item, dict):
                for key, value in item.items():
                    network_info += f"- {key}: {value}\n"
            else:
                network_info += f"- {item}\n"

    # Add network interests if available
    if network_data.get("network_interests") and network_data["network_interests"]:
        network_info += "\nProvided Network Interests:\n"
        network_info += f"- {', '.join(network_data['network_interests'])}\n"

    # Combine all information
    full_profile = user_profile
    if content_info:
        full_profile += "\n" + content_info
    if network_info:
        full_profile += "\n" + network_info

    # Generate general persona using the template with the full profile
    prompt = TEMPLATES["generate_general_persona"].format(user_profile=full_profile)
    response = model.generate_content(prompt)

    # Process the response to extract the JSON
    response_text = response.text.strip()

    # Extract JSON from the response if it's wrapped in markdown code blocks
    if "```json" in response_text:
        json_text = response_text.split("```json")[1].split("```")[0].strip()
    elif "```" in response_text:
        json_text = response_text.split("```")[1].strip()
    else:
        json_text = response_text

    try:
        # Parse the JSON response
        persona_data = json.loads(json_text)

        # Ensure all required fields are present
        if not all(key in persona_data for key in ["general_persona_keywords", "content_interests", "network_interests"]):
            # If missing fields, create a default structure
            persona_data = {
                "general_persona_keywords": persona_data.get("general_persona_keywords", []),
                "content_interests": persona_data.get("content_interests", []),
                "network_interests": persona_data.get("network_interests", [])
            }
    except json.JSONDecodeError:
        # If JSON parsing fails, extract data manually
        general_persona_keywords = []
        content_interests = []
        network_interests = []

        # Simple fallback parsing
        lines = response_text.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if "general_persona_keywords" in line.lower():
                current_section = "general_persona_keywords"
            elif "content_interests" in line.lower():
                current_section = "content_interests"
            elif "network_interests" in line.lower():
                current_section = "network_interests"
            elif line and current_section and (line.startswith('-') or line.startswith('"') or line.startswith("'")):
                # Clean up the line
                clean_line = line.strip('- "\'[],:')
                if clean_line:
                    if current_section == "general_persona_keywords":
                        general_persona_keywords.append(clean_line)
                    elif current_section == "content_interests":
                        content_interests.append(clean_line)
                    elif current_section == "network_interests":
                        network_interests.append(clean_line)

        persona_data = {
            "general_persona_keywords": general_persona_keywords,
            "content_interests": content_interests,
            "network_interests": network_interests
        }

    # Get provided content and network interests from the request
    provided_content_interests = content_data.get("content_interests", [])
    provided_network_interests = network_data.get("network_interests", [])

    # Combine AI-generated content interests with provided content interests
    all_content_interests = list(set(persona_data["content_interests"] + provided_content_interests))

    # Combine AI-generated network interests with provided network interests
    all_network_interests = list(set(persona_data["network_interests"] + provided_network_interests))

    # Update the persona data with the combined interests
    persona_data["content_interests"] = all_content_interests
    persona_data["network_interests"] = all_network_interests

    # Extract keywords from content data if available
    content_data_keywords = []
    if content_data.get("data") and isinstance(content_data["data"], dict) and content_data["data"]:
        # Process content data to extract relevant keywords
        for key, value in content_data["data"].items():
            # Use a more sophisticated approach to extract potential keywords from the content
            if isinstance(value, str):
                # Generate keywords using the model
                prompt = f"Extract 5-7 professional keywords (1-3 words each) from this LinkedIn post. Return ONLY a comma-separated list of keywords, no numbering or explanations:\n\n{value}"
                try:
                    response = model.generate_content(prompt)
                    response_text = response.text.strip()

                    # Split the response by commas and clean up each keyword
                    extracted_keywords = [keyword.strip().lower() for keyword in response_text.split(',')]

                    # Add the extracted keywords to the content_data_keywords list
                    content_data_keywords.extend(extracted_keywords)
                except Exception as e:
                    # Fallback to simple word extraction if model generation fails
                    words = value.lower().split()
                    for word in words:
                        clean_word = word.strip('.,!?;:()[]{}"\'-')
                        if len(clean_word) > 3 and clean_word not in ["and", "the", "for", "with", "that", "this", "have", "from"]:
                            content_data_keywords.append(clean_word)

    # Extract keywords from network data if available
    network_data_keywords = []
    if network_data.get("data") and isinstance(network_data["data"], dict) and network_data["data"]:
        # Process network data to extract relevant keywords
        for key, value in network_data["data"].items():
            # Use a more sophisticated approach to extract potential keywords from the network connections
            if isinstance(value, str):
                # Generate keywords using the model
                prompt = f"Extract 3-5 professional roles, industries, or expertise areas from this LinkedIn connection description. Return ONLY a comma-separated list, no numbering or explanations:\n\n{value}"
                try:
                    response = model.generate_content(prompt)
                    response_text = response.text.strip()

                    # Split the response by commas and clean up each keyword
                    extracted_keywords = [keyword.strip().lower() for keyword in response_text.split(',')]

                    # Add the extracted keywords to the network_data_keywords list
                    network_data_keywords.extend(extracted_keywords)
                except Exception as e:
                    # Fallback to simple word extraction if model generation fails
                    words = value.lower().split()
                    for word in words:
                        clean_word = word.strip('.,!?;:()[]{}"\'-')
                        if len(clean_word) > 3 and clean_word not in ["and", "the", "for", "with", "that", "this", "have", "from"]:
                            network_data_keywords.append(clean_word)

    # Generate content_persona_keywords by combining general_persona_keywords, content interests, and content data keywords
    # This is based on user_profile, content_interests, and content data
    content_persona_keywords = persona_data["general_persona_keywords"].copy()

    # Add content interests
    for interest in all_content_interests:
        # Add interest to content_persona_keywords if it's not already there
        if interest not in content_persona_keywords:
            content_persona_keywords.append(interest)

    # Add content data keywords
    for keyword in content_data_keywords:
        # Add keyword to content_persona_keywords if it's not already there
        if keyword not in content_persona_keywords:
            content_persona_keywords.append(keyword)

    # Generate network_persona_keywords by combining general_persona_keywords, network interests, and network data keywords
    # This is based on user_profile, network_interests, and network data
    network_persona_keywords = persona_data["general_persona_keywords"].copy()

    # Add network interests
    for interest in all_network_interests:
        # Add interest to network_persona_keywords if it's not already there
        if interest not in network_persona_keywords:
            network_persona_keywords.append(interest)

    # Add network data keywords
    for keyword in network_data_keywords:
        # Add keyword to network_persona_keywords if it's not already there
        if keyword not in network_persona_keywords:
            network_persona_keywords.append(keyword)

    # Add the new fields to the persona_data
    persona_data["content_persona_keywords"] = content_persona_keywords
    persona_data["network_persona_keywords"] = network_persona_keywords

    # Add information about the data sources used
    persona_data["data_sources"] = {
        "content_data_used": bool(content_data.get("data")),
        "network_data_used": bool(network_data.get("data")),
        "provided_content_interests_used": bool(provided_content_interests),
        "provided_network_interests_used": bool(provided_network_interests)
    }

    # Add debug information to help understand how the keywords were generated
    persona_data["debug_info"] = {
        "content_data_keywords": content_data_keywords,
        "network_data_keywords": network_data_keywords,
        "provided_content_interests": provided_content_interests,
        "provided_network_interests": provided_network_interests
    }

    return persona_data
