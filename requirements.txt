# Web framework and server
fastapi
uvicorn

# Data validation and parsing
pydantic
python-multipart

# Google Cloud and Vertex AI for the generative model
google-cloud-aiplatform
google-auth

# HTTP requests and web scraping
requests
aiohttp # For asynchronous HTTP requests
beautifulsoup4 # Kept for other potential project parts
trafilatura # ADDED: Required for robust web article text extraction in the new script

# LangChain and LangGraph for agentic architecture
langchain
langgraph
langchain-google-vertexai
langchain-community
langchain-core
tavily-python

# Other application components (if used elsewhere in your project)
streamlit
pandas
python-docx
PyPDF2
openpyxl

# Performance and utility packages
asyncio-throttle
cachetools
redis
emoji