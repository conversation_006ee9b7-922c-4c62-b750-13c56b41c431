#!/bin/bash

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
else
    echo "✅ Docker is installed"
    docker --version
fi

# Check if Docker is running
if docker info &> /dev/null; then
    echo "✅ Docker daemon is running"
else
    echo "❌ Docker daemon is not running"
    echo "Please start Docker and try again"
    exit 1
fi

# Check if our container is running
if docker ps | grep "linkedin-api" &> /dev/null; then
    echo "✅ LinkedIn API container is running"
    
    # Get container details
    echo -e "\nContainer details:"
    docker ps --filter "name=linkedin-api" --format "table {{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
    
    # Get container IP
    CONTAINER_ID=$(docker ps --filter "name=linkedin-api" --format "{{.ID}}")
    if [ ! -z "$CONTAINER_ID" ]; then
        CONTAINER_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $CONTAINER_ID)
        echo -e "\nContainer IP: $CONTAINER_IP"
        echo "API should be accessible at http://************:8003"
    fi
else
    echo "❌ LinkedIn API container is not running"
    
    # Check if the container exists but is stopped
    if docker ps -a | grep "linkedin-api" &> /dev/null; then
        echo "The container exists but is not running"
        echo "You can start it with: docker-compose up -d"
    else
        echo "The container does not exist"
        echo "You can build and run it with: ./docker_test.sh"
    fi
fi
